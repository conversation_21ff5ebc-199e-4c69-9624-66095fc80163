<script setup lang="ts">
import { ref } from 'vue'
import AppButton from '@/components/ui/AppButton.vue'

interface Props {
  modelValue: boolean
  onAgree?: () => void
  onDisagree?: () => void
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
}>()

const showModal = ref(false)

function openAgreement() {
  showModal.value = true
}

function closeAgreement() {
  showModal.value = false
}

function handleAgree() {
  emit('update:modelValue', true)
  props.onAgree?.()
  closeAgreement()
}

function handleDisagree() {
  emit('update:modelValue', false)
  props.onDisagree?.()
  closeAgreement()
}

function toggleAgreement() {
  emit('update:modelValue', !props.modelValue)
}
</script>

<template>
  <div>
    <!-- 协议勾选框 -->
    <label class="flex items-start space-x-2 cursor-pointer">
      <input
        type="checkbox"
        :checked="modelValue"
        @change="toggleAgreement"
        class="mt-1 rounded border-[#E5E7EB] text-[#2563EB] focus:ring-[#2563EB] focus:ring-offset-0"
      />
      <span class="text-sm text-[#6B7280] font-inter leading-relaxed">
        我已阅读并同意
        <button
          type="button"
          @click="openAgreement"
          class="text-[#2563EB] hover:text-[#1D4ED8] underline transition-colors duration-200"
        >
          《用户服务协议》
        </button>
        和
        <button
          type="button"
          @click="openAgreement"
          class="text-[#2563EB] hover:text-[#1D4ED8] underline transition-colors duration-200"
        >
          《隐私政策》
        </button>
      </span>
    </label>

    <!-- 协议弹窗 -->
    <div
      v-if="showModal"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      @click="closeAgreement"
    >
      <div
        class="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] flex flex-col"
        @click.stop
      >
        <!-- 弹窗头部 -->
        <div class="flex items-center justify-between p-6 border-b border-[#E5E7EB]">
          <h3 class="text-lg font-semibold text-[#111827] font-inter">用户服务协议与隐私政策</h3>
          <button
            @click="closeAgreement"
            class="text-[#6B7280] hover:text-[#111827] transition-colors duration-200"
          >
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- 协议内容 -->
        <div class="flex-1 overflow-y-auto p-6">
          <div class="prose prose-sm max-w-none">
            <h4 class="text-base font-semibold text-[#111827] mb-3 font-inter">用户服务协议</h4>
            <div class="text-sm text-[#6B7280] space-y-3 font-inter leading-relaxed">
              <p>
                欢迎使用疾控医护任职资格考试系统（以下简称"本系统"）。在使用本系统前，请您仔细阅读并充分理解本协议的各项条款。
              </p>
              
              <p><strong>1. 服务内容</strong></p>
              <p>
                本系统为医护人员提供任职资格考试相关的学习资料、在线练习、模拟考试等服务，帮助用户提升专业技能和通过相关考试。
              </p>
              
              <p><strong>2. 用户义务</strong></p>
              <ul class="list-disc list-inside space-y-1">
                <li>提供真实、准确的个人信息</li>
                <li>妥善保管账号和密码</li>
                <li>遵守考试纪律和相关规定</li>
                <li>不得恶意攻击系统或传播有害信息</li>
              </ul>
              
              <p><strong>3. 知识产权</strong></p>
              <p>
                本系统的所有内容，包括但不限于文字、图片、音频、视频、软件等，均受知识产权法保护。未经授权，不得复制、传播或用于商业用途。
              </p>
              
              <p><strong>4. 免责声明</strong></p>
              <p>
                本系统仅提供学习和练习平台，不保证用户一定能通过相关考试。考试结果以官方认定为准。
              </p>
            </div>

            <h4 class="text-base font-semibold text-[#111827] mb-3 mt-6 font-inter">隐私政策</h4>
            <div class="text-sm text-[#6B7280] space-y-3 font-inter leading-relaxed">
              <p>
                我们非常重视您的隐私保护。本隐私政策说明我们如何收集、使用、存储和保护您的个人信息。
              </p>
              
              <p><strong>1. 信息收集</strong></p>
              <p>
                我们可能收集以下信息：基本身份信息、联系方式、学习记录、考试成绩等。这些信息用于提供更好的服务和验证用户身份。
              </p>
              
              <p><strong>2. 信息使用</strong></p>
              <ul class="list-disc list-inside space-y-1">
                <li>提供个性化的学习服务</li>
                <li>统计分析和服务改进</li>
                <li>身份验证和安全保护</li>
                <li>法律法规要求的其他用途</li>
              </ul>
              
              <p><strong>3. 信息保护</strong></p>
              <p>
                我们采用行业标准的安全措施保护您的个人信息，包括数据加密、访问控制、安全审计等。
              </p>
              
              <p><strong>4. 信息共享</strong></p>
              <p>
                除法律法规要求外，我们不会向第三方分享您的个人信息。在必要时，我们可能与合作伙伴共享匿名化的统计数据。
              </p>
            </div>
          </div>
        </div>

        <!-- 弹窗底部 -->
        <div class="flex items-center justify-end space-x-3 p-6 border-t border-[#E5E7EB]">
          <AppButton variant="outline" @click="handleDisagree">
            不同意
          </AppButton>
          <AppButton variant="primary" @click="handleAgree">
            同意并继续
          </AppButton>
        </div>
      </div>
    </div>
  </div>
</template>
