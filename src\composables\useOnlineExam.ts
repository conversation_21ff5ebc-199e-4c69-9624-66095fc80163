import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'
import type { Question, ExamRecord } from '@/types'
import {
  getExamQuestions,
  submitExamAnswer,
  submitExam,
  saveExamProgress,
  reportAntiCheatEvent,
  checkExamEnvironment,
  requestTimeExtension
} from '@/api/exam'

export function useOnlineExam(recordId: string) {
  const queryClient = useQueryClient()
  
  // 状态管理
  const currentQuestionIndex = ref(0)
  const answers = ref<Record<string, string | string[]>>({})
  const timeRemaining = ref(0)
  const isSubmitted = ref(false)
  const isFullscreen = ref(false)
  const examStartTime = ref<Date | null>(null)
  const lastSaveTime = ref<Date | null>(null)
  
  // 防作弊监控
  const tabSwitchCount = ref(0)
  const fullscreenExitCount = ref(0)
  const suspiciousActivityCount = ref(0)
  
  // 定时器
  let countdownTimer: NodeJS.Timeout | null = null
  let autoSaveTimer: NodeJS.Timeout | null = null
  let heartbeatTimer: NodeJS.Timeout | null = null

  // 获取考试题目和状态
  const {
    data: examData,
    isLoading: isLoadingQuestions,
    error: questionsError,
    refetch: refetchQuestions
  } = useQuery({
    queryKey: ['exam', 'questions', recordId],
    queryFn: () => getExamQuestions(recordId),
    enabled: computed(() => !!recordId && !isSubmitted.value),
    refetchInterval: 30000, // 30秒刷新一次，获取最新的剩余时间
    staleTime: 0, // 总是重新获取
  })

  // 计算属性
  const questions = computed(() => examData.value?.questions || [])
  const currentQuestion = computed(() => questions.value[currentQuestionIndex.value])
  const totalQuestions = computed(() => questions.value.length)
  const answeredCount = computed(() => Object.keys(answers.value).length)
  const progress = computed(() => 
    totalQuestions.value > 0 ? (answeredCount.value / totalQuestions.value) * 100 : 0
  )

  // 格式化剩余时间
  const formattedTimeRemaining = computed(() => {
    const hours = Math.floor(timeRemaining.value / 3600)
    const minutes = Math.floor((timeRemaining.value % 3600) / 60)
    const seconds = timeRemaining.value % 60
    
    if (hours > 0) {
      return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    }
    return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
  })

  // 检查是否时间不足
  const isTimeRunningOut = computed(() => timeRemaining.value <= 300) // 5分钟
  const isTimeCritical = computed(() => timeRemaining.value <= 60) // 1分钟

  // 提交单题答案
  const submitAnswerMutation = useMutation({
    mutationFn: ({ questionId, answer }: { questionId: string; answer: string | string[] }) =>
      submitExamAnswer(recordId, questionId, answer),
    onSuccess: (data) => {
      // 更新剩余时间
      timeRemaining.value = data.timeRemaining
    }
  })

  // 提交整份考试
  const submitExamMutation = useMutation({
    mutationFn: (examAnswers: Record<string, string | string[]>) =>
      submitExam(recordId, examAnswers),
    onSuccess: () => {
      isSubmitted.value = true
      stopAllTimers()
      // 清除查询缓存
      queryClient.removeQueries({ queryKey: ['exam', 'questions', recordId] })
    }
  })

  // 保存考试进度
  const saveProgressMutation = useMutation({
    mutationFn: (examAnswers: Record<string, string | string[]>) =>
      saveExamProgress(recordId, examAnswers),
    onSuccess: () => {
      lastSaveTime.value = new Date()
    }
  })

  // 报告防作弊事件
  const reportEventMutation = useMutation({
    mutationFn: ({ eventType, details }: { 
      eventType: 'tab_switch' | 'fullscreen_exit' | 'copy_paste' | 'right_click' | 'suspicious_activity'
      details?: Record<string, any> 
    }) => reportAntiCheatEvent(recordId, eventType, details)
  })

  // 初始化考试数据
  function initializeExam() {
    if (examData.value) {
      timeRemaining.value = examData.value.timeRemaining
      answers.value = examData.value.currentAnswers || {}
      examStartTime.value = new Date()
      
      // 启动定时器
      startCountdownTimer()
      startAutoSaveTimer()
      startHeartbeatTimer()
    }
  }

  // 启动倒计时定时器
  function startCountdownTimer() {
    if (countdownTimer) clearInterval(countdownTimer)
    
    countdownTimer = setInterval(() => {
      if (timeRemaining.value > 0) {
        timeRemaining.value--
      } else {
        // 时间到，自动提交
        handleAutoSubmit()
      }
    }, 1000)
  }

  // 启动自动保存定时器
  function startAutoSaveTimer() {
    if (autoSaveTimer) clearInterval(autoSaveTimer)
    
    autoSaveTimer = setInterval(() => {
      if (Object.keys(answers.value).length > 0) {
        saveProgressMutation.mutate(answers.value)
      }
    }, 30000) // 30秒自动保存一次
  }

  // 启动心跳定时器
  function startHeartbeatTimer() {
    if (heartbeatTimer) clearInterval(heartbeatTimer)
    
    heartbeatTimer = setInterval(() => {
      // 刷新考试状态，确保连接正常
      refetchQuestions()
    }, 60000) // 1分钟心跳一次
  }

  // 停止所有定时器
  function stopAllTimers() {
    if (countdownTimer) {
      clearInterval(countdownTimer)
      countdownTimer = null
    }
    if (autoSaveTimer) {
      clearInterval(autoSaveTimer)
      autoSaveTimer = null
    }
    if (heartbeatTimer) {
      clearInterval(heartbeatTimer)
      heartbeatTimer = null
    }
  }

  // 切换到指定题目
  function goToQuestion(index: number) {
    if (index >= 0 && index < totalQuestions.value) {
      currentQuestionIndex.value = index
    }
  }

  // 下一题
  function nextQuestion() {
    if (currentQuestionIndex.value < totalQuestions.value - 1) {
      currentQuestionIndex.value++
    }
  }

  // 上一题
  function previousQuestion() {
    if (currentQuestionIndex.value > 0) {
      currentQuestionIndex.value--
    }
  }

  // 保存答案
  function saveAnswer(questionId: string, answer: string | string[]) {
    answers.value[questionId] = answer
    
    // 提交单题答案到服务器
    submitAnswerMutation.mutate({ questionId, answer })
  }

  // 手动提交考试
  async function submitExamManually() {
    try {
      await submitExamMutation.mutateAsync(answers.value)
      return true
    } catch (error) {
      console.error('提交考试失败:', error)
      return false
    }
  }

  // 自动提交考试（时间到）
  async function handleAutoSubmit() {
    stopAllTimers()
    try {
      await submitExamMutation.mutateAsync(answers.value)
    } catch (error) {
      console.error('自动提交考试失败:', error)
    }
  }

  // 手动保存进度
  function saveProgress() {
    if (Object.keys(answers.value).length > 0) {
      saveProgressMutation.mutate(answers.value)
    }
  }

  // 报告防作弊事件
  function reportAntiCheat(eventType: Parameters<typeof reportEventMutation.mutate>[0]['eventType'], details?: Record<string, any>) {
    reportEventMutation.mutate({ eventType, details })
    
    // 更新计数
    switch (eventType) {
      case 'tab_switch':
        tabSwitchCount.value++
        break
      case 'fullscreen_exit':
        fullscreenExitCount.value++
        break
      case 'suspicious_activity':
        suspiciousActivityCount.value++
        break
    }
  }

  // 进入全屏
  function enterFullscreen() {
    const element = document.documentElement
    if (element.requestFullscreen) {
      element.requestFullscreen()
    }
  }

  // 退出全屏
  function exitFullscreen() {
    if (document.exitFullscreen) {
      document.exitFullscreen()
    }
  }

  // 监听全屏状态变化
  function handleFullscreenChange() {
    isFullscreen.value = !!document.fullscreenElement
    
    if (!isFullscreen.value && examStartTime.value) {
      // 退出全屏，报告事件
      reportAntiCheat('fullscreen_exit', {
        timestamp: new Date().toISOString()
      })
    }
  }

  // 监听页面可见性变化
  function handleVisibilityChange() {
    if (document.hidden && examStartTime.value) {
      // 页面隐藏，可能是切换标签页
      reportAntiCheat('tab_switch', {
        timestamp: new Date().toISOString()
      })
    }
  }

  // 监听右键点击
  function handleContextMenu(event: MouseEvent) {
    event.preventDefault()
    reportAntiCheat('right_click', {
      timestamp: new Date().toISOString(),
      target: (event.target as Element)?.tagName
    })
  }

  // 监听复制粘贴
  function handleCopyPaste(event: ClipboardEvent) {
    event.preventDefault()
    reportAntiCheat('copy_paste', {
      timestamp: new Date().toISOString(),
      type: event.type
    })
  }

  // 生命周期管理
  onMounted(() => {
    // 监听全屏变化
    document.addEventListener('fullscreenchange', handleFullscreenChange)
    
    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange)
    
    // 禁用右键菜单
    document.addEventListener('contextmenu', handleContextMenu)
    
    // 禁用复制粘贴
    document.addEventListener('copy', handleCopyPaste)
    document.addEventListener('paste', handleCopyPaste)
    document.addEventListener('cut', handleCopyPaste)
    
    // 初始化考试
    if (examData.value) {
      initializeExam()
    }
  })

  onUnmounted(() => {
    // 清理定时器
    stopAllTimers()
    
    // 移除事件监听器
    document.removeEventListener('fullscreenchange', handleFullscreenChange)
    document.removeEventListener('visibilitychange', handleVisibilityChange)
    document.removeEventListener('contextmenu', handleContextMenu)
    document.removeEventListener('copy', handleCopyPaste)
    document.removeEventListener('paste', handleCopyPaste)
    document.removeEventListener('cut', handleCopyPaste)
  })

  // 监听考试数据变化
  computed(() => {
    if (examData.value && !examStartTime.value) {
      initializeExam()
    }
  })

  return {
    // 状态
    currentQuestionIndex,
    answers,
    timeRemaining,
    isSubmitted,
    isFullscreen,
    examStartTime,
    lastSaveTime,
    
    // 防作弊计数
    tabSwitchCount,
    fullscreenExitCount,
    suspiciousActivityCount,
    
    // 查询数据
    questions,
    currentQuestion,
    totalQuestions,
    answeredCount,
    progress,
    
    // 加载状态
    isLoadingQuestions,
    questionsError,
    
    // 变更状态
    isSubmittingAnswer: computed(() => submitAnswerMutation.isPending),
    isSubmittingExam: computed(() => submitExamMutation.isPending),
    isSavingProgress: computed(() => saveProgressMutation.isPending),
    
    // 计算属性
    formattedTimeRemaining,
    isTimeRunningOut,
    isTimeCritical,
    
    // 方法
    goToQuestion,
    nextQuestion,
    previousQuestion,
    saveAnswer,
    submitExamManually,
    saveProgress,
    reportAntiCheat,
    enterFullscreen,
    exitFullscreen,
    refetchQuestions
  }
}
