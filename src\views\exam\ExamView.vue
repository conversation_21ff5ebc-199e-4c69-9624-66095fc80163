<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useExam } from '@/composables/useExam'
import { useNotificationStore } from '@/stores/notificationStore'
import { useAuthStore } from '@/stores/authStore'
import GuestPrompt from '@/components/common/GuestPrompt.vue'
import AppCard from '@/components/ui/AppCard.vue'
import AppButton from '@/components/ui/AppButton.vue'
import type { Exam, ExamRecord } from '@/types'

const router = useRouter()
const notificationStore = useNotificationStore()
const authStore = useAuthStore()

// 使用考试中心 composable
const {
  examOverview,
  upcomingExams,
  recentExamRecords,
  examStats,
  isLoadingOverview,
  isLoadingUpcoming,
  isLoadingRecent,
  isLoadingStats,
  overviewError,
  upcomingError,
  recentError,
  statsError,
  canRegisterExam,
  canStartExam,
  getUserExamRecord,
  registerOfflineExamAction,
  startOnlineExamAction,
  isRegisteringOfflineExam,
  isStartingOnlineExam,
  refetchOverview,
  refetchUpcoming,
  refetchRecent,
} = useExam()

// 本地状态
const showExamDetail = ref(false)
const selectedExam = ref<Exam | null>(null)

// 计算属性
const hasUpcomingExams = computed(() => upcomingExams.value && upcomingExams.value.length > 0)
const hasRecentRecords = computed(
  () => recentExamRecords.value && recentExamRecords.value.length > 0,
)
const isLoading = computed(
  () => isLoadingOverview.value || isLoadingUpcoming.value || isLoadingRecent.value,
)

// 格式化日期
function formatDate(dateString: string): string {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

// 格式化相对时间
function formatRelativeTime(dateString: string): string {
  const date = new Date(dateString)
  const now = new Date()
  const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60))

  if (diffInHours < 1) return '刚刚'
  if (diffInHours < 24) return `${diffInHours}小时前`
  if (diffInHours < 24 * 7) return `${Math.floor(diffInHours / 24)}天前`
  return formatDate(dateString)
}

// 生命周期
onMounted(() => {
  refetchOverview()
  refetchUpcoming()
  refetchRecent()
})

// 获取考试状态文本
function getExamStatusText(exam: Exam): string {
  const userRecord = getUserExamRecord(exam.id)

  if (userRecord) {
    switch (userRecord.status) {
      case 'registered':
        return '已报名'
      case 'in_progress':
        return '进行中'
      case 'completed':
        return '已完成'
      case 'absent':
        return '缺考'
      default:
        return '未知状态'
    }
  }

  switch (exam.status) {
    case 'pending':
      return '即将开始'
    case 'ongoing':
      return '报名中'
    case 'completed':
      return '已结束'
    case 'cancelled':
      return '已取消'
    default:
      return '未知状态'
  }
}

// 获取考试状态样式
function getExamStatusClass(exam: Exam): string {
  const userRecord = getUserExamRecord(exam.id)

  if (userRecord) {
    switch (userRecord.status) {
      case 'registered':
        return 'bg-blue-100 text-blue-800'
      case 'in_progress':
        return 'bg-yellow-100 text-yellow-800'
      case 'completed':
        return 'bg-green-100 text-green-800'
      case 'absent':
        return 'bg-red-100 text-red-800'
      default:
        return 'bg-gray-100 text-gray-800'
    }
  }

  switch (exam.status) {
    case 'pending':
      return 'bg-purple-100 text-purple-800'
    case 'ongoing':
      return 'bg-green-100 text-green-800'
    case 'completed':
      return 'bg-gray-100 text-gray-800'
    case 'cancelled':
      return 'bg-red-100 text-red-800'
    default:
      return 'bg-gray-100 text-gray-800'
  }
}

// 获取考试类型文本
function getExamTypeText(type: string): string {
  return type === 'online' ? '线上考试' : '线下考试'
}

// 获取考试类型图标
function getExamTypeIcon(type: string): string {
  return type === 'online' ? '💻' : '📝'
}

// 获取活动类型图标
function getActivityIcon(type: string): string {
  switch (type) {
    case 'exam_completed':
      return '✅'
    case 'exam_registered':
      return '📝'
    case 'certificate_issued':
      return '🏆'
    default:
      return '📋'
  }
}

// 检查考试是否可以报名
function canRegisterExamCheck(exam: Exam): { canRegister: boolean; reason?: string } {
  const now = new Date()
  const registrationDeadline = new Date(exam.registrationDeadline)
  const userRecord = getUserExamRecord(exam.id)

  if (userRecord) {
    return { canRegister: false, reason: '您已报名该考试' }
  }

  if (now > registrationDeadline) {
    return { canRegister: false, reason: '报名时间已截止' }
  }

  if (exam.status !== 'ongoing') {
    return { canRegister: false, reason: '考试未开放报名' }
  }

  return { canRegister: true }
}

// 检查考试是否可以开始
function canStartExamCheck(exam: Exam): { canStart: boolean; reason?: string } {
  if (exam.type === 'offline') {
    return { canStart: false, reason: '线下考试请按时到场参加' }
  }

  const now = new Date()
  const startTime = new Date(exam.startTime)
  const endTime = new Date(exam.endTime)
  const userRecord = getUserExamRecord(exam.id)

  if (!userRecord) {
    return { canStart: false, reason: '请先报名该考试' }
  }

  if (userRecord.status === 'completed') {
    return { canStart: false, reason: '您已完成该考试' }
  }

  if (userRecord.status === 'absent') {
    return { canStart: false, reason: '您已被标记为缺考' }
  }

  if (now < startTime) {
    return { canStart: false, reason: '考试尚未开始' }
  }

  if (now > endTime) {
    return { canStart: false, reason: '考试时间已结束' }
  }

  return { canStart: true }
}

// 处理考试操作
async function handleExamAction(exam: Exam, action: string) {
  try {
    switch (action) {
      case 'register': {
        // 检查报名条件
        const { canRegister, reason } = canRegisterExamCheck(exam)
        if (!canRegister) {
          notificationStore.addNotification({
            type: 'warning',
            title: '无法报名',
            message: reason || '报名条件不满足',
          })
          return
        }

        // 线下考试报名逻辑（需要选择场次）
        if (exam.type === 'offline') {
          router.push(`/exam/${exam.id}`)
        } else {
          // 线上考试直接报名
          await registerOfflineExamAction(exam.id, '')
          notificationStore.addNotification({
            type: 'success',
            title: '报名成功',
            message: '您已成功报名该考试',
          })
        }
        break
      }

      case 'start': {
        // 检查开始考试条件
        const { canStart, reason } = canStartExamCheck(exam)
        if (!canStart) {
          notificationStore.addNotification({
            type: 'warning',
            title: '无法开始考试',
            message: reason || '开始考试条件不满足',
          })
          return
        }

        // 开始线上考试
        const result = await startOnlineExamAction(exam.id)
        router.push(`/exam/${exam.id}/take/${result.recordId}`)
        break
      }

      case 'view':
        // 查看考试详情
        router.push(`/exam/${exam.id}`)
        break

      default:
        break
    }
  } catch (error: any) {
    notificationStore.addNotification({
      type: 'error',
      title: '操作失败',
      message: error.message || '操作失败，请稍后重试',
    })
  }
}

// 处理查看考试结果
function handleViewResult(record: ExamRecord) {
  router.push(`/exam/${record.examId}/result/${record.id}`)
}

// 跳转到考试列表页面
function goToExamList() {
  router.push('/exam/list')
}

// 获取认证状态标题
function getVerificationStatusTitle(): string {
  switch (authStore.verificationStatus) {
    case 'unverified':
      return '需要完成身份认证'
    case 'pending':
      return '身份认证审核中'
    case 'rejected':
      return '身份认证未通过'
    default:
      return '需要完成身份认证'
  }
}

// 获取认证状态消息
function getVerificationStatusMessage(): string {
  switch (authStore.verificationStatus) {
    case 'unverified':
      return '您需要先完成身份认证才能报名和参加考试。请前往个人中心完善您的身份信息。'
    case 'pending':
      return '您的身份认证正在审核中，审核通过后即可报名参加考试。'
    case 'rejected':
      return '您的身份认证未通过，请重新提交相关材料。'
    default:
      return '请完成身份认证以参加考试功能。'
  }
}
</script>

<template>
  <div class="min-h-screen bg-[#F8F9FA]">
    <!-- 访客提示 -->
    <GuestPrompt
      v-if="!authStore.isAuthenticated"
      title="登录以参加考试"
      message="您可以查看考试信息，但需要登录后才能报名和参加考试。登录后系统将自动跳转回当前页面。"
    />

    <!-- 认证状态提示 -->
    <AppCard
      v-else-if="!authStore.isVerifiedUser"
      padding="md"
      shadow="sm"
      class="mb-6 bg-[#FEF3C7] border border-[#F59E0B]"
    >
      <div class="flex items-center space-x-3">
        <div class="flex-shrink-0">
          <svg class="w-5 h-5 text-[#F59E0B]" fill="currentColor" viewBox="0 0 20 20">
            <path
              fill-rule="evenodd"
              d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
              clip-rule="evenodd"
            />
          </svg>
        </div>
        <div>
          <h3 class="text-sm font-semibold text-[#92400E] font-inter">
            {{ getVerificationStatusTitle() }}
          </h3>
          <p class="text-sm text-[#92400E] mt-1 font-inter">
            {{ getVerificationStatusMessage() }}
          </p>
        </div>
        <div class="flex-shrink-0">
          <AppButton variant="secondary" size="sm" @click="router.push({ name: 'profile-info' })">
            前往认证
          </AppButton>
        </div>
      </div>
    </AppCard>

    <div class="space-y-6">
      <!-- 页面头部 -->
      <AppCard padding="lg" shadow="sm">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-[#111827] font-inter">考试中心</h1>
            <p class="mt-2 text-[#6B7280] leading-relaxed font-inter">参加在线考试，获取专业认证</p>
          </div>

          <!-- 考试统计概览 -->
          <div v-if="examOverview" class="flex items-center space-x-4">
            <div class="text-right">
              <div class="text-sm text-[#6B7280] font-medium font-inter">下次考试</div>
              <div v-if="examOverview.nextExam" class="text-lg font-semibold text-[#111827] font-inter">
                {{ examOverview.nextExam.title }}
              </div>
              <div v-else class="text-lg font-semibold text-[#6B7280] font-inter">暂无安排</div>
              <div v-if="examOverview.nextExam" class="text-sm text-[#6B7280] font-inter">
                {{ formatDate(examOverview.nextExam.startTime) }}
              </div>
            </div>
            <div class="text-6xl">📝</div>
          </div>

          <div v-else class="text-6xl">📝</div>
        </div>
      </AppCard>

      <!-- 考试统计卡片 -->
      <div v-if="examOverview" class="grid grid-cols-2 lg:grid-cols-6 gap-4">
        <AppCard
          padding="md"
          shadow="sm"
          hover
          class="bg-gradient-to-r from-[#EFF6FF] to-[#DBEAFE]"
        >
          <div class="flex items-center">
            <div class="p-2 bg-[#2563EB] rounded-lg shadow-sm">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-[#1E40AF]">考试总数</p>
              <p class="text-xl font-bold text-[#1E3A8A]">{{ examOverview.totalExams }}</p>
            </div>
          </div>
        </AppCard>

        <AppCard
          padding="md"
          shadow="sm"
          hover
          class="bg-gradient-to-r from-[#ECFDF5] to-[#D1FAE5]"
        >
          <div class="flex items-center">
            <div class="p-2 bg-[#10B981] rounded-lg shadow-sm">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-[#047857]">已完成</p>
              <p class="text-xl font-bold text-[#065F46]">{{ examOverview.completedExams }}</p>
            </div>
          </div>
        </AppCard>

        <AppCard
          padding="md"
          shadow="sm"
          hover
          class="bg-gradient-to-r from-[#FEF3C7] to-[#FDE68A]"
        >
          <div class="flex items-center">
            <div class="p-2 bg-[#F59E0B] rounded-lg shadow-sm">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M13 10V3L4 14h7v7l9-11h-7z"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-[#92400E]">通过考试</p>
              <p class="text-xl font-bold text-[#78350F]">{{ examOverview.passedExams }}</p>
            </div>
          </div>
        </AppCard>

        <AppCard
          padding="md"
          shadow="sm"
          hover
          class="bg-gradient-to-r from-[#F3E8FF] to-[#E9D5FF]"
        >
          <div class="flex items-center">
            <div class="p-2 bg-[#8B5CF6] rounded-lg shadow-sm">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-[#6B21A8]">平均分</p>
              <p class="text-xl font-bold text-[#581C87]">
                {{ examOverview.averageScore?.toFixed(1) || '0.0' }}
              </p>
            </div>
          </div>
        </AppCard>

        <AppCard
          padding="md"
          shadow="sm"
          hover
          class="bg-gradient-to-r from-[#EEF2FF] to-[#E0E7FF]"
        >
          <div class="flex items-center">
            <div class="p-2 bg-[#6366F1] rounded-lg shadow-sm">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-[#4338CA]">即将考试</p>
              <p class="text-xl font-bold text-[#3730A3]">{{ examOverview.upcomingExams }}</p>
            </div>
          </div>
        </AppCard>

        <AppCard
          padding="md"
          shadow="sm"
          hover
          class="bg-gradient-to-r from-[#FDF2F8] to-[#FCE7F3]"
        >
          <div class="flex items-center">
            <div class="p-2 bg-[#EC4899] rounded-lg shadow-sm">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                />
              </svg>
            </div>
            <div class="ml-3">
              <p class="text-sm font-medium text-[#BE185D]">获得证书</p>
              <p class="text-xl font-bold text-[#9D174D]">{{ examOverview.certificates }}</p>
            </div>
          </div>
        </AppCard>
      </div>

      <!-- 快捷信息区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- 待考试列表 -->
        <AppCard padding="lg" shadow="sm" hover>
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-semibold text-[#111827] flex items-center space-x-2">
              <span>📋</span>
              <span>待考试列表</span>
            </h3>
            <AppButton
              variant="ghost"
              size="sm"
              @click="goToExamList"
            >
              查看全部 →
            </AppButton>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoadingUpcoming" class="space-y-3">
            <div v-for="i in 3" :key="i" class="animate-pulse">
              <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-8 h-8 bg-gray-200 rounded"></div>
                <div class="flex-1">
                  <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div class="w-16 h-6 bg-gray-200 rounded-full"></div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!hasUpcomingExams" class="text-center py-8">
            <div class="text-4xl mb-2">📝</div>
            <p class="text-gray-500 text-sm">暂无待考试</p>
            <p class="text-gray-400 text-xs mt-1">请关注最新考试安排</p>
          </div>

          <!-- 待考试列表 -->
          <div v-else class="space-y-3">
            <div
              v-for="exam in upcomingExams"
              :key="exam.id"
              class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <div class="text-2xl">{{ getExamTypeIcon(exam.type) }}</div>
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-900 truncate">{{ exam.title }}</h4>
                <p class="text-sm text-gray-500">{{ formatDate(exam.startTime) }}</p>
                <p class="text-xs text-gray-400">{{ getExamTypeText(exam.type) }}</p>
              </div>
              <div class="flex-shrink-0 flex items-center space-x-2">
                <span
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium"
                  :class="getExamStatusClass(exam)"
                >
                  {{ getExamStatusText(exam) }}
                </span>

                <!-- 操作按钮 -->
                <div class="flex space-x-1">
                  <!-- 报名按钮 -->
                  <button
                    v-if="canRegisterExamCheck(exam).canRegister"
                    @click.stop="handleExamAction(exam, 'register')"
                    :disabled="isRegisteringOfflineExam"
                    class="px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded hover:bg-blue-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {{ isRegisteringOfflineExam ? '报名中...' : '报名' }}
                  </button>

                  <!-- 开始考试按钮 -->
                  <button
                    v-else-if="canStartExamCheck(exam).canStart"
                    @click.stop="handleExamAction(exam, 'start')"
                    :disabled="isStartingOnlineExam"
                    class="px-2 py-1 text-xs font-medium text-green-600 bg-green-50 rounded hover:bg-green-100 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                  >
                    {{ isStartingOnlineExam ? '启动中...' : '开始考试' }}
                  </button>

                  <!-- 查看详情按钮 -->
                  <button
                    @click.stop="handleExamAction(exam, 'view')"
                    class="px-2 py-1 text-xs font-medium text-gray-600 bg-gray-50 rounded hover:bg-gray-100 transition-colors"
                  >
                    详情
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最近考试记录 -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="flex items-center justify-between mb-4">
            <h3 class="text-lg font-medium text-gray-900 flex items-center space-x-2">
              <span>📊</span>
              <span>最近考试记录</span>
            </h3>
            <button
              @click="goToExamList"
              class="text-sm text-blue-600 hover:text-blue-800 transition-colors"
            >
              查看全部 →
            </button>
          </div>

          <!-- 加载状态 -->
          <div v-if="isLoadingRecent" class="space-y-3">
            <div v-for="i in 3" :key="i" class="animate-pulse">
              <div class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <div class="w-8 h-8 bg-gray-200 rounded"></div>
                <div class="flex-1">
                  <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                  <div class="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div class="w-12 h-6 bg-gray-200 rounded"></div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-else-if="!hasRecentRecords" class="text-center py-8">
            <div class="text-4xl mb-2">📊</div>
            <p class="text-gray-500 text-sm">暂无考试记录</p>
            <p class="text-gray-400 text-xs mt-1">完成考试后将在这里显示</p>
          </div>

          <!-- 最近考试记录列表 -->
          <div v-else class="space-y-3">
            <div
              v-for="record in recentExamRecords"
              :key="record.id"
              class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors cursor-pointer"
              @click="handleViewResult(record)"
            >
              <div class="text-2xl">{{ getExamTypeIcon(record.exam.type) }}</div>
              <div class="flex-1 min-w-0">
                <h4 class="font-medium text-gray-900 truncate">{{ record.exam.title }}</h4>
                <p class="text-sm text-gray-500">{{ formatRelativeTime(record.createdAt) }}</p>
                <p class="text-xs text-gray-400">{{ getExamTypeText(record.exam.type) }}</p>
              </div>
              <div class="flex-shrink-0 text-right">
                <div v-if="record.score !== undefined" class="text-lg font-semibold text-gray-900">
                  {{ record.score }}分
                </div>
                <div v-else class="text-sm text-gray-500">
                  {{ record.status === 'completed' ? '已完成' : '进行中' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 最近活动 -->
      <div
        v-if="examOverview && examOverview.recentActivity.length > 0"
        class="bg-white rounded-lg shadow-sm border border-gray-200 p-6"
      >
        <h3 class="text-lg font-medium text-gray-900 mb-4 flex items-center space-x-2">
          <span>🔔</span>
          <span>最近活动</span>
        </h3>

        <div class="space-y-3">
          <div
            v-for="activity in examOverview.recentActivity"
            :key="`${activity.type}-${activity.date}`"
            class="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"
          >
            <div class="text-xl">{{ getActivityIcon(activity.type) }}</div>
            <div class="flex-1 min-w-0">
              <h4 class="font-medium text-gray-900">{{ activity.title }}</h4>
              <p class="text-sm text-gray-500">{{ formatRelativeTime(activity.date) }}</p>
              <p v-if="activity.details" class="text-xs text-gray-400">{{ activity.details }}</p>
            </div>
          </div>
        </div>
      </div>

      <!-- 快速操作提示 -->
      <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <svg
              class="w-5 h-5 text-blue-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-blue-800">温馨提示</h3>
            <div class="mt-2 text-sm text-blue-700">
              <ul class="list-disc list-inside space-y-1">
                <li>线上考试可以直接报名并开始考试</li>
                <li>线下考试需要选择具体的考试场次</li>
                <li>请在报名截止时间前完成报名</li>
                <li>考试开始后请确保网络稳定，避免中途断线</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
