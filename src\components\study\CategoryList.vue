<script setup lang="ts">
import { ref, computed } from 'vue'
import type { QuestionCategory, StudyProgress } from '@/types'
import CategoryCard from './CategoryCard.vue'

interface Props {
  categories: QuestionCategory[]
  progress?: StudyProgress[]
  loading?: boolean
}

const props = defineProps<Props>()

const emit = defineEmits<{
  selectCategory: [category: QuestionCategory]
  reviewCategory: [category: QuestionCategory]
  refresh: []
}>()

// 搜索和筛选
const searchKeyword = ref('')
const selectedDifficulty = ref<string>('')

// 筛选后的分类列表
const filteredCategories = computed(() => {
  let filtered = props.categories

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(category => 
      category.name.toLowerCase().includes(keyword) ||
      category.description.toLowerCase().includes(keyword)
    )
  }

  // 按难度筛选
  if (selectedDifficulty.value) {
    filtered = filtered.filter(category => 
      category.difficulty === selectedDifficulty.value
    )
  }

  return filtered
})

// 获取分类的进度信息
function getCategoryProgress(categoryId: string) {
  return props.progress?.find(p => p.categoryId === categoryId)
}

// 清除筛选条件
function clearFilters() {
  searchKeyword.value = ''
  selectedDifficulty.value = ''
}

// 处理分类选择
function handleSelectCategory(category: QuestionCategory) {
  emit('selectCategory', category)
}

// 处理复习错题
function handleReviewCategory(category: QuestionCategory) {
  emit('reviewCategory', category)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 搜索和筛选栏 -->
    <div class="bg-white rounded-lg shadow-sm p-4">
      <div class="flex flex-col sm:flex-row gap-4">
        <!-- 搜索框 -->
        <div class="flex-1">
          <div class="relative">
            <input
              v-model="searchKeyword"
              type="text"
              placeholder="搜索题库分类..."
              class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>

        <!-- 难度筛选 -->
        <div class="sm:w-48">
          <select
            v-model="selectedDifficulty"
            class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">全部难度</option>
            <option value="easy">简单</option>
            <option value="medium">中等</option>
            <option value="hard">困难</option>
          </select>
        </div>

        <!-- 清除筛选 -->
        <button
          v-if="searchKeyword || selectedDifficulty"
          @click="clearFilters"
          class="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
        >
          清除筛选
        </button>

        <!-- 刷新按钮 -->
        <button
          @click="$emit('refresh')"
          class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
          :disabled="loading"
        >
          <svg class="w-4 h-4 inline-block mr-2" :class="{ 'animate-spin': loading }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
          </svg>
          刷新
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-1 sm:grid-cols-3 gap-4">
      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-blue-100 rounded-lg">
            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">题库总数</p>
            <p class="text-2xl font-semibold text-gray-900">{{ categories.length }}</p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-green-100 rounded-lg">
            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">已练习</p>
            <p class="text-2xl font-semibold text-gray-900">
              {{ progress?.filter(p => p.totalPracticed > 0).length || 0 }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-yellow-100 rounded-lg">
            <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均准确率</p>
            <p class="text-2xl font-semibold text-gray-900">
              {{ progress?.length ? (progress.reduce((sum, p) => sum + p.averageAccuracy, 0) / progress.length).toFixed(1) : 0 }}%
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 分类列表 -->
    <div v-if="loading" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <!-- 骨架屏 -->
      <div v-for="i in 6" :key="i" class="bg-white rounded-lg shadow-sm p-6 animate-pulse">
        <div class="flex items-center space-x-3 mb-4">
          <div class="w-12 h-12 bg-gray-200 rounded-lg"></div>
          <div class="flex-1">
            <div class="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4 mb-4">
          <div class="text-center">
            <div class="h-8 bg-gray-200 rounded mb-1"></div>
            <div class="h-3 bg-gray-200 rounded"></div>
          </div>
          <div class="text-center">
            <div class="h-8 bg-gray-200 rounded mb-1"></div>
            <div class="h-3 bg-gray-200 rounded"></div>
          </div>
        </div>
        <div class="h-10 bg-gray-200 rounded"></div>
      </div>
    </div>

    <div v-else-if="filteredCategories.length === 0" class="text-center py-12">
      <div class="text-gray-400 text-6xl mb-4">📚</div>
      <h3 class="text-lg font-medium text-gray-900 mb-2">暂无题库分类</h3>
      <p class="text-gray-600">
        {{ searchKeyword || selectedDifficulty ? '没有找到符合条件的题库分类' : '系统中还没有题库分类' }}
      </p>
    </div>

    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      <CategoryCard
        v-for="category in filteredCategories"
        :key="category.id"
        :category="category"
        :progress="getCategoryProgress(category.id)"
        @select="handleSelectCategory"
        @review="handleReviewCategory"
      />
    </div>
  </div>
</template>
