import { request } from './index'
import type { User, Certificate, PaginationParams, PaginationResponse } from '@/types'

// 个人中心相关API接口

/**
 * 获取当前用户信息
 */
export function getCurrentUser(): Promise<User> {
  return request.get('/profile/me')
}

/**
 * 更新个人信息
 * @param data 个人信息数据
 */
export function updateProfile(data: Partial<User>): Promise<User> {
  return request.put('/profile/me', data)
}

/**
 * 上传头像
 * @param file 头像文件
 */
export function uploadAvatar(file: File): Promise<{ url: string }> {
  const formData = new FormData()
  formData.append('avatar', file)
  
  return request.post('/profile/avatar', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

/**
 * 提交认证申请
 * @param data 认证申请数据
 */
export function submitCertification(data: {
  realName: string
  idCard: string
  workUnit: string
  position: string
  phone: string
  email: string
  attachments?: Array<{
    name: string
    url: string
    type: string
  }>
}): Promise<void> {
  return request.post('/profile/certification', data)
}

/**
 * 获取认证状态
 */
export function getCertificationStatus(): Promise<{
  status: 'pending' | 'approved' | 'rejected' | 'not_submitted'
  submittedAt?: string
  reviewedAt?: string
  reviewComment?: string
  reviewedBy?: string
}> {
  return request.get('/profile/certification/status')
}

/**
 * 获取用户证书列表
 * @param params 查询参数
 */
export function getUserCertificates(
  params?: Partial<PaginationParams & { status?: Certificate['status'] }>
): Promise<PaginationResponse<Certificate>> {
  return request.get('/profile/certificates', { params })
}

/**
 * 获取证书详情
 * @param certificateId 证书ID
 */
export function getCertificateDetail(certificateId: string): Promise<Certificate> {
  return request.get(`/profile/certificates/${certificateId}`)
}

/**
 * 下载证书
 * @param certificateId 证书ID
 */
export function downloadCertificate(certificateId: string): Promise<Blob> {
  return request.get(`/profile/certificates/${certificateId}/download`, {
    responseType: 'blob'
  })
}

/**
 * 获取证书预览URL
 * @param certificateId 证书ID
 */
export function getCertificatePreviewUrl(certificateId: string): Promise<{ url: string }> {
  return request.get(`/profile/certificates/${certificateId}/preview`)
}

/**
 * 申请证书重新颁发
 * @param certificateId 证书ID
 * @param reason 申请原因
 */
export function requestCertificateReissue(
  certificateId: string, 
  reason: string
): Promise<void> {
  return request.post(`/profile/certificates/${certificateId}/reissue`, { reason })
}

/**
 * 修改密码
 * @param data 密码修改数据
 */
export function changePassword(data: {
  currentPassword: string
  newPassword: string
  confirmPassword: string
}): Promise<void> {
  return request.put('/profile/password', data)
}

/**
 * 绑定手机号
 * @param data 手机号绑定数据
 */
export function bindPhone(data: {
  phone: string
  verificationCode: string
}): Promise<void> {
  return request.post('/profile/bind-phone', data)
}

/**
 * 发送手机验证码
 * @param phone 手机号
 */
export function sendPhoneVerificationCode(phone: string): Promise<void> {
  return request.post('/profile/send-phone-code', { phone })
}

/**
 * 绑定邮箱
 * @param data 邮箱绑定数据
 */
export function bindEmail(data: {
  email: string
  verificationCode: string
}): Promise<void> {
  return request.post('/profile/bind-email', data)
}

/**
 * 发送邮箱验证码
 * @param email 邮箱地址
 */
export function sendEmailVerificationCode(email: string): Promise<void> {
  return request.post('/profile/send-email-code', { email })
}

/**
 * 获取个人统计数据
 */
export function getProfileStats(): Promise<{
  totalExams: number
  passedExams: number
  totalCertificates: number
  validCertificates: number
  totalStudyTime: number // 分钟
  totalPracticeQuestions: number
  averageScore: number
  lastLoginTime: string
  registrationDays: number
}> {
  return request.get('/profile/stats')
}

/**
 * 获取活动日志
 * @param params 查询参数
 */
export function getActivityLogs(
  params?: Partial<PaginationParams & { 
    type?: 'login' | 'exam' | 'study' | 'certificate' | 'profile'
    startDate?: string
    endDate?: string
  }>
): Promise<PaginationResponse<{
  id: string
  type: string
  action: string
  description: string
  metadata?: Record<string, any>
  ipAddress?: string
  userAgent?: string
  createdAt: string
}>> {
  return request.get('/profile/activity-logs', { params })
}

/**
 * 注销账户申请
 * @param reason 注销原因
 */
export function requestAccountDeletion(reason: string): Promise<void> {
  return request.post('/profile/delete-account', { reason })
}

/**
 * 导出个人数据
 */
export function exportPersonalData(): Promise<Blob> {
  return request.get('/profile/export-data', {
    responseType: 'blob'
  })
}
