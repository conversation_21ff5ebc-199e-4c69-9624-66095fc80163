<script setup lang="ts">
import { computed } from 'vue'
import type { PracticeResult, QuestionResult } from '@/types'

interface Props {
  result: PracticeResult
}

const props = defineProps<Props>()

const emit = defineEmits<{
  restart: []
  reviewWrong: []
  backToCategories: []
}>()

// 计算属性
const accuracyColor = computed(() => {
  const accuracy = props.result.accuracy
  if (accuracy >= 80) return 'text-green-600'
  if (accuracy >= 60) return 'text-yellow-600'
  return 'text-red-600'
})

const accuracyBgColor = computed(() => {
  const accuracy = props.result.accuracy
  if (accuracy >= 80) return 'bg-green-100'
  if (accuracy >= 60) return 'bg-yellow-100'
  return 'bg-red-100'
})

const performanceLevel = computed(() => {
  const accuracy = props.result.accuracy
  if (accuracy >= 90) return { text: '优秀', emoji: '🎉' }
  if (accuracy >= 80) return { text: '良好', emoji: '👍' }
  if (accuracy >= 60) return { text: '及格', emoji: '😊' }
  return { text: '需要加强', emoji: '💪' }
})

// 格式化时间
const formattedDuration = computed(() => {
  const minutes = Math.floor(props.result.duration / 60)
  const seconds = props.result.duration % 60
  return `${minutes}分${seconds}秒`
})

// 平均答题时间
const averageTimePerQuestion = computed(() => {
  const avgSeconds = Math.floor(props.result.duration / props.result.totalQuestions)
  return `${avgSeconds}秒/题`
})

// 错题分析
const wrongQuestionsByType = computed(() => {
  const typeMap = new Map<string, QuestionResult[]>()
  
  props.result.wrongQuestions.forEach(result => {
    const type = result.question.type
    if (!typeMap.has(type)) {
      typeMap.set(type, [])
    }
    typeMap.get(type)!.push(result)
  })
  
  return Array.from(typeMap.entries()).map(([type, questions]) => ({
    type,
    typeName: getTypeName(type),
    count: questions.length,
    questions
  }))
})

function getTypeName(type: string): string {
  const typeNames = {
    single: '单选题',
    multiple: '多选题',
    judge: '判断题',
    fill: '填空题'
  }
  return typeNames[type as keyof typeof typeNames] || type
}

// 获取选项内容
function getOptionContent(question: any, optionId: string): string {
  const option = question.options?.find((opt: any) => opt.id === optionId)
  return option ? `${option.label}. ${option.content}` : optionId
}

// 格式化答案显示
function formatAnswer(answer: string | string[], question: any): string {
  if (Array.isArray(answer)) {
    return answer.map(a => getOptionContent(question, a)).join(', ')
  }
  
  if (question.type === 'judge') {
    return answer === 'true' ? '正确' : '错误'
  }
  
  if (question.type === 'fill') {
    return answer
  }
  
  return getOptionContent(question, answer)
}
</script>

<template>
  <div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
      <!-- 结果概览 -->
      <div class="bg-white rounded-lg shadow-sm p-8 mb-8">
        <div class="text-center mb-8">
          <div class="text-6xl mb-4">{{ performanceLevel.emoji }}</div>
          <h1 class="text-3xl font-bold text-gray-900 mb-2">练习完成！</h1>
          <p class="text-lg text-gray-600">{{ result.categoryName }} - 练习结果</p>
        </div>

        <!-- 成绩统计 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div class="text-center">
            <div class="text-3xl font-bold mb-2" :class="accuracyColor">
              {{ result.accuracy.toFixed(1) }}%
            </div>
            <div class="text-sm text-gray-600">正确率</div>
            <div class="text-xs text-gray-500 mt-1">{{ performanceLevel.text }}</div>
          </div>
          
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900 mb-2">
              {{ result.correctAnswers }}
            </div>
            <div class="text-sm text-gray-600">答对题数</div>
            <div class="text-xs text-gray-500 mt-1">共 {{ result.totalQuestions }} 题</div>
          </div>
          
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900 mb-2">
              {{ result.score }}
            </div>
            <div class="text-sm text-gray-600">得分</div>
            <div class="text-xs text-gray-500 mt-1">满分 100 分</div>
          </div>
          
          <div class="text-center">
            <div class="text-3xl font-bold text-gray-900 mb-2">
              {{ formattedDuration }}
            </div>
            <div class="text-sm text-gray-600">用时</div>
            <div class="text-xs text-gray-500 mt-1">{{ averageTimePerQuestion }}</div>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <button
            @click="$emit('restart')"
            class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium"
          >
            再来一组
          </button>
          
          <button
            v-if="result.wrongQuestions.length > 0"
            @click="$emit('reviewWrong')"
            class="px-6 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 transition-colors font-medium"
          >
            复习错题 ({{ result.wrongQuestions.length }})
          </button>
          
          <button
            @click="$emit('backToCategories')"
            class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
          >
            返回题库
          </button>
        </div>
      </div>

      <!-- 错题分析 -->
      <div v-if="result.wrongQuestions.length > 0" class="bg-white rounded-lg shadow-sm p-6">
        <h2 class="text-xl font-bold text-gray-900 mb-6">错题分析</h2>
        
        <!-- 错题类型统计 -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div
            v-for="typeGroup in wrongQuestionsByType"
            :key="typeGroup.type"
            class="bg-red-50 rounded-lg p-4 text-center"
          >
            <div class="text-2xl font-bold text-red-600 mb-1">{{ typeGroup.count }}</div>
            <div class="text-sm text-red-800">{{ typeGroup.typeName }}</div>
          </div>
        </div>

        <!-- 错题详情 -->
        <div class="space-y-6">
          <h3 class="text-lg font-medium text-gray-900">错题详情</h3>
          
          <div class="space-y-4">
            <div
              v-for="(wrongResult, index) in result.wrongQuestions"
              :key="wrongResult.question.id"
              class="border border-red-200 rounded-lg p-4 bg-red-50"
            >
              <!-- 题目信息 -->
              <div class="flex items-start justify-between mb-3">
                <div class="flex items-center space-x-2">
                  <span class="bg-red-100 text-red-800 px-2 py-1 rounded text-sm font-medium">
                    错题 {{ index + 1 }}
                  </span>
                  <span class="text-sm text-gray-600">
                    {{ getTypeName(wrongResult.question.type) }}
                  </span>
                </div>
                <span v-if="wrongResult.timeSpent" class="text-sm text-gray-500">
                  用时: {{ wrongResult.timeSpent }}秒
                </span>
              </div>

              <!-- 题目内容 -->
              <div class="mb-4">
                <h4 class="font-medium text-gray-900 mb-2">{{ wrongResult.question.title }}</h4>
                <div class="text-gray-700 text-sm" v-html="wrongResult.question.content"></div>
              </div>

              <!-- 答案对比 -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <div class="text-sm font-medium text-red-700 mb-1">您的答案:</div>
                  <div class="text-sm text-red-600 bg-red-100 rounded p-2">
                    {{ formatAnswer(wrongResult.userAnswer, wrongResult.question) }}
                  </div>
                </div>
                <div>
                  <div class="text-sm font-medium text-green-700 mb-1">正确答案:</div>
                  <div class="text-sm text-green-600 bg-green-100 rounded p-2">
                    {{ formatAnswer(wrongResult.correctAnswer, wrongResult.question) }}
                  </div>
                </div>
              </div>

              <!-- 解析 -->
              <div v-if="wrongResult.question.explanation" class="bg-blue-50 rounded p-3">
                <div class="text-sm font-medium text-blue-900 mb-1">解析:</div>
                <div class="text-sm text-blue-800" v-html="wrongResult.question.explanation"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 无错题时的鼓励 -->
      <div v-else class="bg-white rounded-lg shadow-sm p-8 text-center">
        <div class="text-6xl mb-4">🎯</div>
        <h2 class="text-2xl font-bold text-gray-900 mb-2">全部答对！</h2>
        <p class="text-gray-600">恭喜您在这次练习中表现完美，继续保持！</p>
      </div>
    </div>
  </div>
</template>
