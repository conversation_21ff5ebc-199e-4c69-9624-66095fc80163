<script setup lang="ts">
import { ref, computed } from 'vue'
import { useQuery } from '@tanstack/vue-query'
import type { ExamRecord } from '@/types'
import * as examApi from '@/api/exam'

interface Props {
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  viewResult: [record: ExamRecord]
  retakeExam: [examId: string]
  refresh: []
}>()

// 状态管理
const selectedStatus = ref<ExamRecord['status'] | ''>('')
const currentPage = ref(1)
const pageSize = ref(10)

// 查询参数
const queryParams = computed(() => ({
  page: currentPage.value,
  pageSize: pageSize.value,
  status: selectedStatus.value || undefined
}))

// 获取考试记录
const {
  data: recordsData,
  isLoading: isLoadingRecords,
  refetch: refetchRecords
} = useQuery({
  queryKey: ['exam', 'records', queryParams],
  queryFn: () => examApi.getUserExamRecords(queryParams.value),
  staleTime: 1000 * 60 * 5, // 5分钟
})

// 计算属性
const records = computed(() => recordsData.value?.items || [])
const totalRecords = computed(() => recordsData.value?.total || 0)
const totalPages = computed(() => recordsData.value?.totalPages || 1)

// 统计信息
const stats = computed(() => {
  const total = records.value.length
  const completed = records.value.filter(r => r.status === 'completed').length
  const passed = records.value.filter(r => r.status === 'completed' && r.score && r.score >= (r.exam?.passScore || 60)).length
  const registered = records.value.filter(r => r.status === 'registered').length
  
  return { total, completed, passed, registered }
})

// 方法
function handleViewResult(record: ExamRecord) {
  emit('viewResult', record)
}

function handleRetakeExam(examId: string) {
  emit('retakeExam', examId)
}

function handleRefresh() {
  refetchRecords()
  emit('refresh')
}

function changePage(page: number) {
  currentPage.value = page
}

// 获取状态显示文本
function getStatusText(status: ExamRecord['status']): string {
  const statusMap = {
    registered: '已报名',
    in_progress: '考试中',
    completed: '已完成',
    absent: '缺考'
  }
  return statusMap[status] || status
}

// 获取状态颜色
function getStatusColor(status: ExamRecord['status']): string {
  const colorMap = {
    registered: 'text-blue-600 bg-blue-100',
    in_progress: 'text-purple-600 bg-purple-100',
    completed: 'text-green-600 bg-green-100',
    absent: 'text-red-600 bg-red-100'
  }
  return colorMap[status] || 'text-gray-600 bg-gray-100'
}

// 格式化日期
function formatDate(dateString: string): string {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 检查是否通过
function isPassed(record: ExamRecord): boolean {
  return record.status === 'completed' && 
         record.score !== undefined && 
         record.score >= (record.exam?.passScore || 60)
}

// 检查是否可以重考
function canRetake(record: ExamRecord): boolean {
  return record.status === 'completed' && !isPassed(record)
}
</script>

<template>
  <div class="space-y-6">
    <!-- 筛选和统计 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between mb-4">
        <h2 class="text-xl font-semibold text-gray-900">考试历史</h2>
        
        <!-- 筛选器 -->
        <div class="flex items-center space-x-4">
          <select
            v-model="selectedStatus"
            class="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          >
            <option value="">全部状态</option>
            <option value="registered">已报名</option>
            <option value="in_progress">考试中</option>
            <option value="completed">已完成</option>
            <option value="absent">缺考</option>
          </select>
          
          <button
            @click="handleRefresh"
            class="px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors"
            :disabled="isLoadingRecords"
          >
            <svg class="w-4 h-4 inline-block mr-2" :class="{ 'animate-spin': isLoadingRecords }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            刷新
          </button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
        <div class="bg-blue-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="p-2 bg-blue-100 rounded-lg">
              <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-blue-600">考试总数</p>
              <p class="text-2xl font-semibold text-blue-900">{{ stats.total }}</p>
            </div>
          </div>
        </div>

        <div class="bg-green-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="p-2 bg-green-100 rounded-lg">
              <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-green-600">通过考试</p>
              <p class="text-2xl font-semibold text-green-900">{{ stats.passed }}</p>
            </div>
          </div>
        </div>

        <div class="bg-purple-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="p-2 bg-purple-100 rounded-lg">
              <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-purple-600">已完成</p>
              <p class="text-2xl font-semibold text-purple-900">{{ stats.completed }}</p>
            </div>
          </div>
        </div>

        <div class="bg-yellow-50 rounded-lg p-4">
          <div class="flex items-center">
            <div class="p-2 bg-yellow-100 rounded-lg">
              <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            </div>
            <div class="ml-4">
              <p class="text-sm font-medium text-yellow-600">待考试</p>
              <p class="text-2xl font-semibold text-yellow-900">{{ stats.registered }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 考试记录列表 -->
    <div class="bg-white rounded-lg shadow-sm overflow-hidden">
      <div v-if="isLoadingRecords" class="p-6">
        <!-- 骨架屏 -->
        <div class="space-y-4">
          <div v-for="i in 5" :key="i" class="animate-pulse">
            <div class="flex items-center space-x-4 p-4 border-b border-gray-200">
              <div class="w-12 h-12 bg-gray-200 rounded"></div>
              <div class="flex-1">
                <div class="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
                <div class="h-3 bg-gray-200 rounded w-1/2"></div>
              </div>
              <div class="w-20 h-6 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>

      <div v-else-if="records.length === 0" class="text-center py-12">
        <div class="text-gray-400 text-6xl mb-4">📋</div>
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无考试记录</h3>
        <p class="text-gray-600">您还没有参加过任何考试</p>
      </div>

      <div v-else class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                考试信息
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                类型
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                状态
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                成绩
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                时间
              </th>
              <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                操作
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="record in records" :key="record.id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div>
                  <div class="text-sm font-medium text-gray-900">{{ record.exam?.title }}</div>
                  <div class="text-sm text-gray-500">{{ record.exam?.description }}</div>
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span class="text-sm text-gray-900">
                  {{ record.exam?.type === 'online' ? '线上考试' : '线下考试' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="px-2 py-1 text-xs font-medium rounded-full"
                  :class="getStatusColor(record.status)"
                >
                  {{ getStatusText(record.status) }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div v-if="record.status === 'completed' && record.score !== undefined">
                  <div class="text-sm font-medium" 
                       :class="isPassed(record) ? 'text-green-600' : 'text-red-600'">
                    {{ record.score }}分
                  </div>
                  <div class="text-xs text-gray-500">
                    {{ isPassed(record) ? '通过' : '未通过' }}
                  </div>
                </div>
                <div v-else class="text-sm text-gray-400">--</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div>{{ formatDate(record.startTime) }}</div>
                <div v-if="record.endTime" class="text-xs">
                  至 {{ formatDate(record.endTime) }}
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm space-x-2">
                <button
                  v-if="record.status === 'completed'"
                  @click="handleViewResult(record)"
                  class="text-blue-600 hover:text-blue-800 transition-colors"
                >
                  查看详情
                </button>
                
                <button
                  v-if="canRetake(record)"
                  @click="handleRetakeExam(record.examId)"
                  class="text-green-600 hover:text-green-800 transition-colors"
                >
                  重新考试
                </button>
                
                <span v-if="record.status === 'registered'" class="text-gray-400">
                  等待考试
                </span>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div v-if="totalPages > 1" class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="text-sm text-gray-700">
            共 {{ totalRecords }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页
          </div>
          <div class="flex space-x-2">
            <button
              @click="changePage(currentPage - 1)"
              :disabled="currentPage === 1"
              class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              上一页
            </button>
            <button
              @click="changePage(currentPage + 1)"
              :disabled="currentPage === totalPages"
              class="px-3 py-1 text-sm border border-gray-300 rounded hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              下一页
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
