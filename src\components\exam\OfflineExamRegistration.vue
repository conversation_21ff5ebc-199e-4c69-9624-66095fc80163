<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'
import { useNotificationStore } from '@/stores/notificationStore'
import type { Exam } from '@/types'
import * as examApi from '@/api/exam'
import AppButton from '@/components/ui/AppButton.vue'

interface Props {
  exam: Exam
}

const props = defineProps<Props>()

const emit = defineEmits<{
  registered: [sessionId: string]
  cancel: []
}>()

const notificationStore = useNotificationStore()
const queryClient = useQueryClient()

// 状态管理
const selectedSessionId = ref('')
const showConfirm = ref(false)

// 获取考试场次
const {
  data: sessions,
  isLoading: isLoadingSessions,
  error: sessionsError,
  refetch: refetchSessions
} = useQuery({
  queryKey: ['exam', 'sessions', props.exam.id],
  queryFn: () => examApi.getExamSessions(props.exam.id),
  staleTime: 1000 * 60 * 5, // 5分钟
})

// 报名考试
const registerMutation = useMutation({
  mutationFn: ({ examId, sessionId }: { examId: string; sessionId: string }) =>
    examApi.registerOfflineExam(examId, sessionId),
  onSuccess: (record) => {
    notificationStore.success('报名成功', '您已成功报名线下考试')
    emit('registered', selectedSessionId.value)
    
    // 刷新相关数据
    queryClient.invalidateQueries({ queryKey: ['exam', 'sessions'] })
    queryClient.invalidateQueries({ queryKey: ['exam', 'records'] })
  },
  onError: (error) => {
    notificationStore.error('报名失败', '考试报名失败，请稍后重试')
    console.error('Register exam failed:', error)
  }
})

// 计算属性
const availableSessions = computed(() => {
  return sessions.value?.filter(session => 
    session.status === 'open' && session.available > 0
  ) || []
})

const selectedSession = computed(() => {
  return sessions.value?.find(session => session.id === selectedSessionId.value)
})

// 生命周期
onMounted(() => {
  refetchSessions()
})

// 方法
function selectSession(sessionId: string) {
  selectedSessionId.value = sessionId
}

function confirmRegistration() {
  if (!selectedSessionId.value) {
    notificationStore.warning('请选择场次', '请先选择一个考试场次')
    return
  }
  showConfirm.value = true
}

async function submitRegistration() {
  if (!selectedSessionId.value) return
  
  try {
    await registerMutation.mutateAsync({
      examId: props.exam.id,
      sessionId: selectedSessionId.value
    })
    showConfirm.value = false
  } catch (error) {
    // 错误已在 mutation 中处理
  }
}

function cancelRegistration() {
  showConfirm.value = false
  emit('cancel')
}

// 格式化日期时间
function formatDateTime(date: string, startTime: string, endTime: string): string {
  const dateObj = new Date(date)
  const dateStr = dateObj.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    weekday: 'short'
  })
  return `${dateStr} ${startTime}-${endTime}`
}

// 获取场次状态颜色
function getSessionStatusColor(session: any): string {
  if (session.status === 'full') return 'text-red-600 bg-red-100'
  if (session.status === 'closed') return 'text-gray-600 bg-gray-100'
  if (session.available <= 5) return 'text-yellow-600 bg-yellow-100'
  return 'text-green-600 bg-green-100'
}

// 获取场次状态文本
function getSessionStatusText(session: any): string {
  if (session.status === 'full') return '已满'
  if (session.status === 'closed') return '已关闭'
  if (session.available <= 5) return `仅剩${session.available}个名额`
  return `剩余${session.available}个名额`
}
</script>

<template>
  <div class="space-y-6">
    <!-- 考试信息 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <h2 class="text-xl font-semibold text-gray-900 mb-4">线下考试报名</h2>
      
      <div class="bg-blue-50 rounded-lg p-4 mb-6">
        <h3 class="font-medium text-blue-900 mb-2">{{ exam.title }}</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm text-blue-800">
          <div>
            <span class="font-medium">考试时长:</span>
            <span class="ml-2">{{ exam.duration }}分钟</span>
          </div>
          <div>
            <span class="font-medium">题目数量:</span>
            <span class="ml-2">{{ exam.totalQuestions }}题</span>
          </div>
          <div>
            <span class="font-medium">及格分数:</span>
            <span class="ml-2">{{ exam.passScore }}分</span>
          </div>
          <div>
            <span class="font-medium">报名截止:</span>
            <span class="ml-2">{{ new Date(exam.registrationDeadline).toLocaleString('zh-CN') }}</span>
          </div>
        </div>
      </div>

      <!-- 报名须知 -->
      <div class="bg-yellow-50 rounded-lg p-4 mb-6">
        <h4 class="font-medium text-yellow-900 mb-2">📋 报名须知</h4>
        <ul class="text-sm text-yellow-800 space-y-1">
          <li>• 请提前30分钟到达考试地点</li>
          <li>• 携带有效身份证件和准考证</li>
          <li>• 考试开始后15分钟不得入场</li>
          <li>• 考试期间不得使用手机等电子设备</li>
          <li>• 如需取消报名，请在考试前24小时操作</li>
        </ul>
      </div>

      <!-- 报名要求 -->
      <div v-if="exam.requirements && exam.requirements.length > 0" class="bg-gray-50 rounded-lg p-4 mb-6">
        <h4 class="font-medium text-gray-900 mb-2">📝 报名要求</h4>
        <ul class="text-sm text-gray-700 space-y-1">
          <li v-for="requirement in exam.requirements" :key="requirement">
            • {{ requirement }}
          </li>
        </ul>
      </div>
    </div>

    <!-- 场次选择 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">选择考试场次</h3>
      
      <!-- 加载状态 -->
      <div v-if="isLoadingSessions" class="space-y-4">
        <div v-for="i in 3" :key="i" class="animate-pulse">
          <div class="border border-gray-200 rounded-lg p-4">
            <div class="h-4 bg-gray-200 rounded w-1/3 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
            <div class="h-3 bg-gray-200 rounded w-1/4"></div>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="sessionsError" class="text-center py-8">
        <div class="text-red-500 text-4xl mb-4">⚠️</div>
        <h4 class="text-lg font-medium text-gray-900 mb-2">加载失败</h4>
        <p class="text-gray-600 mb-4">无法加载考试场次信息</p>
        <AppButton @click="refetchSessions" variant="outline">
          重新加载
        </AppButton>
      </div>

      <!-- 无可用场次 -->
      <div v-else-if="!availableSessions.length" class="text-center py-8">
        <div class="text-gray-400 text-4xl mb-4">📅</div>
        <h4 class="text-lg font-medium text-gray-900 mb-2">暂无可用场次</h4>
        <p class="text-gray-600">当前没有开放的考试场次，请稍后再试</p>
      </div>

      <!-- 场次列表 -->
      <div v-else class="space-y-4">
        <div
          v-for="session in availableSessions"
          :key="session.id"
          class="border rounded-lg p-4 cursor-pointer transition-all duration-200"
          :class="{
            'border-blue-500 bg-blue-50': selectedSessionId === session.id,
            'border-gray-200 hover:border-gray-300': selectedSessionId !== session.id
          }"
          @click="selectSession(session.id)"
        >
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-3">
              <!-- 选择框 -->
              <div class="flex-shrink-0">
                <div 
                  class="w-5 h-5 rounded-full border-2 flex items-center justify-center"
                  :class="selectedSessionId === session.id ? 'border-blue-500 bg-blue-500' : 'border-gray-300'"
                >
                  <div v-if="selectedSessionId === session.id" class="w-2 h-2 rounded-full bg-white"></div>
                </div>
              </div>
              
              <!-- 场次信息 -->
              <div>
                <div class="font-medium text-gray-900">
                  {{ formatDateTime(session.date, session.startTime, session.endTime) }}
                </div>
                <div class="text-sm text-gray-600 mt-1">
                  📍 {{ session.location }}
                </div>
                <div class="text-sm text-gray-500 mt-1">
                  容量: {{ session.capacity }}人 | 已报名: {{ session.registered }}人
                </div>
              </div>
            </div>
            
            <!-- 状态标签 -->
            <span 
              class="px-3 py-1 text-xs font-medium rounded-full"
              :class="getSessionStatusColor(session)"
            >
              {{ getSessionStatusText(session) }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex space-x-4">
      <AppButton
        @click="cancelRegistration"
        variant="outline"
        class="flex-1"
      >
        取消
      </AppButton>
      
      <AppButton
        @click="confirmRegistration"
        :disabled="!selectedSessionId || registerMutation.isPending"
        class="flex-1"
      >
        确认报名
      </AppButton>
    </div>

    <!-- 确认弹窗 -->
    <div
      v-if="showConfirm"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
    >
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4">
        <h3 class="text-lg font-semibold text-gray-900 mb-4">确认报名</h3>
        
        <div v-if="selectedSession" class="space-y-3 mb-6">
          <div class="bg-gray-50 rounded-lg p-3">
            <div class="font-medium text-gray-900 mb-2">{{ exam.title }}</div>
            <div class="text-sm text-gray-600 space-y-1">
              <div>考试时间: {{ formatDateTime(selectedSession.date, selectedSession.startTime, selectedSession.endTime) }}</div>
              <div>考试地点: {{ selectedSession.location }}</div>
              <div>剩余名额: {{ selectedSession.available }}个</div>
            </div>
          </div>
        </div>

        <p class="text-sm text-gray-600 mb-6">
          确认报名后，请按时参加考试。如需取消，请在考试前24小时操作。
        </p>

        <div class="flex space-x-3">
          <button
            @click="showConfirm = false"
            class="flex-1 px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
            :disabled="registerMutation.isPending"
          >
            取消
          </button>
          <AppButton
            @click="submitRegistration"
            :loading="registerMutation.isPending"
            class="flex-1"
          >
            确认报名
          </AppButton>
        </div>
      </div>
    </div>
  </div>
</template>
