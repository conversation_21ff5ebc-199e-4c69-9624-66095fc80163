<script setup lang="ts">
import { computed } from 'vue'
import type { QuestionCategory } from '@/types'

interface Props {
  category: QuestionCategory
  progress?: {
    practiced: number
    accuracy: number
  }
}

const props = defineProps<Props>()

const emit = defineEmits<{
  select: [category: QuestionCategory]
}>()

// 难度颜色映射
const difficultyColors = {
  easy: 'bg-green-100 text-green-800',
  medium: 'bg-yellow-100 text-yellow-800', 
  hard: 'bg-red-100 text-red-800'
}

// 难度文本映射
const difficultyText = {
  easy: '简单',
  medium: '中等',
  hard: '困难'
}

// 计算进度百分比
const progressPercentage = computed(() => {
  if (!props.progress) return 0
  return Math.min(100, (props.progress.practiced / props.category.questionCount) * 100)
})

// 计算准确率颜色
const accuracyColor = computed(() => {
  if (!props.progress) return 'text-gray-500'
  const accuracy = props.progress.accuracy
  if (accuracy >= 80) return 'text-green-600'
  if (accuracy >= 60) return 'text-yellow-600'
  return 'text-red-600'
})

function handleClick() {
  emit('select', props.category)
}
</script>

<template>
  <div 
    class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow cursor-pointer group"
    @click="handleClick"
  >
    <!-- 头部 -->
    <div class="flex items-start justify-between mb-4">
      <div class="flex items-center space-x-3">
        <!-- 图标 -->
        <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center group-hover:bg-blue-200 transition-colors">
          <span class="text-2xl">{{ category.icon || '📚' }}</span>
        </div>
        
        <!-- 标题和描述 -->
        <div>
          <h3 class="text-lg font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
            {{ category.name }}
          </h3>
          <p class="text-sm text-gray-600 mt-1">{{ category.description }}</p>
        </div>
      </div>
      
      <!-- 难度标签 -->
      <span 
        class="px-2 py-1 text-xs font-medium rounded-full"
        :class="difficultyColors[category.difficulty]"
      >
        {{ difficultyText[category.difficulty] }}
      </span>
    </div>

    <!-- 统计信息 -->
    <div class="grid grid-cols-2 gap-4 mb-4">
      <div class="text-center">
        <div class="text-2xl font-bold text-gray-900">{{ category.questionCount }}</div>
        <div class="text-sm text-gray-600">题目总数</div>
      </div>
      
      <div class="text-center" v-if="progress">
        <div class="text-2xl font-bold" :class="accuracyColor">
          {{ progress.accuracy.toFixed(1) }}%
        </div>
        <div class="text-sm text-gray-600">准确率</div>
      </div>
      
      <div class="text-center" v-else>
        <div class="text-2xl font-bold text-gray-400">--</div>
        <div class="text-sm text-gray-600">准确率</div>
      </div>
    </div>

    <!-- 进度条 -->
    <div v-if="progress" class="mb-4">
      <div class="flex justify-between text-sm text-gray-600 mb-2">
        <span>练习进度</span>
        <span>{{ progress.practiced }}/{{ category.questionCount }}</span>
      </div>
      <div class="w-full bg-gray-200 rounded-full h-2">
        <div 
          class="bg-blue-600 h-2 rounded-full transition-all duration-300"
          :style="{ width: `${progressPercentage}%` }"
        ></div>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="flex space-x-2">
      <button 
        class="flex-1 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium"
        @click.stop="$emit('select', category)"
      >
        开始练习
      </button>
      
      <button 
        v-if="progress && progress.practiced > 0"
        class="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm font-medium"
        @click.stop="$emit('review', category)"
      >
        复习错题
      </button>
    </div>
  </div>
</template>
