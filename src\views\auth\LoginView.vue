<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { useNotificationStore } from '@/stores/notificationStore'
import AppButton from '@/components/ui/AppButton.vue'
import AppInput from '@/components/ui/AppInput.vue'
import WeChatLogin from '@/components/auth/WeChatLogin.vue'
import UserAgreement from '@/components/auth/UserAgreement.vue'

const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

const isLoading = ref(false)
const showWeChatLogin = ref(false)
const rememberMe = ref(false)
const agreedToTerms = ref(false)
const loginForm = ref({
  username: '',
  password: '',
})

// 计算属性
const canSubmit = computed(() => {
  return (
    loginForm.value.username && loginForm.value.password && agreedToTerms.value && !isLoading.value
  )
})

async function handleLogin() {
  if (!loginForm.value.username || !loginForm.value.password) {
    notificationStore.warning('请填写完整信息', '用户名和密码不能为空')
    return
  }

  if (!agreedToTerms.value) {
    notificationStore.warning('请同意协议', '请先阅读并同意用户服务协议和隐私政策')
    return
  }

  isLoading.value = true

  try {
    // 模拟登录API调用
    await new Promise((resolve) => setTimeout(resolve, 1000))

    // 模拟登录成功
    const mockUser = {
      id: '1',
      username: loginForm.value.username,
      realName: '张三',
      workUnit: '某疾控中心',
      position: '医师',
      permissions: ['info:read', 'study:read', 'exam:read', 'profile:read'],
      email: '<EMAIL>',
      phone: '13800138000',
      idCard: '110101199001011234',
      verificationStatus: 'approved' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    }

    authStore.setAuth({
      token: 'mock-jwt-token',
      user: mockUser,
      expiresIn: 3600,
    })

    // 保存登录状态（如果勾选了记住我）
    if (rememberMe.value) {
      localStorage.setItem('rememberLogin', 'true')
    }

    notificationStore.success('登录成功', '欢迎使用疾控医护任职资格考试系统')

    // 登录上下文恢复 - 跳转到之前访问的页面或默认页面
    const redirectPath = (router.currentRoute.value.query.redirect as string) || '/'
    router.push(redirectPath)
  } catch (error) {
    notificationStore.error('登录失败', '用户名或密码错误')
  } finally {
    isLoading.value = false
  }
}

function toggleLoginMethod() {
  showWeChatLogin.value = !showWeChatLogin.value
}

function handleWeChatLoginSuccess() {
  // 微信登录成功后的处理
  const redirectPath = (router.currentRoute.value.query.redirect as string) || '/'
  router.push(redirectPath)
}

function handleForgotPassword() {
  notificationStore.info('找回密码', '找回密码功能正在开发中，请联系管理员重置密码')
}
</script>

<template>
  <div
    class="min-h-screen flex items-center justify-center bg-[#F8F9FA] py-12 px-4 sm:px-6 lg:px-8"
  >
    <div class="max-w-md w-full space-y-8">
      <!-- 头部 -->
      <div class="text-center">
        <div
          class="mx-auto w-16 h-16 bg-[#2563EB] rounded-lg flex items-center justify-center mb-4 shadow-lg"
        >
          <span class="text-white font-bold text-xl font-inter">CDC</span>
        </div>
        <h1 class="text-3xl font-bold text-[#111827] font-inter">疾控考试系统</h1>
        <p class="mt-2 text-[#6B7280] font-inter">医护任职资格考试平台</p>
      </div>

      <!-- 登录表单容器 -->
      <div class="bg-white rounded-lg shadow-lg p-8">
        <!-- 登录方式切换 -->
        <div class="flex mb-6 bg-[#F8F9FA] rounded-lg p-1">
          <button
            type="button"
            @click="showWeChatLogin = false"
            :class="[
              'flex-1 py-2 px-4 rounded-md text-sm font-medium font-inter transition-all duration-200',
              !showWeChatLogin
                ? 'bg-white text-[#2563EB] shadow-sm'
                : 'text-[#6B7280] hover:text-[#111827]',
            ]"
          >
            账号登录
          </button>
          <button
            type="button"
            @click="showWeChatLogin = true"
            :class="[
              'flex-1 py-2 px-4 rounded-md text-sm font-medium font-inter transition-all duration-200',
              showWeChatLogin
                ? 'bg-white text-[#2563EB] shadow-sm'
                : 'text-[#6B7280] hover:text-[#111827]',
            ]"
          >
            微信登录
          </button>
        </div>

        <!-- 账号登录表单 -->
        <form v-if="!showWeChatLogin" @submit.prevent="handleLogin" class="space-y-6">
          <AppInput
            v-model="loginForm.username"
            label="用户名"
            placeholder="请输入用户名"
            required
          />

          <AppInput
            v-model="loginForm.password"
            type="password"
            label="密码"
            placeholder="请输入密码"
            required
          />

          <div class="flex items-center justify-between">
            <label class="flex items-center cursor-pointer">
              <input
                v-model="rememberMe"
                type="checkbox"
                class="rounded border-[#E5E7EB] text-[#2563EB] focus:ring-[#2563EB] focus:ring-offset-0"
              />
              <span class="ml-2 text-sm text-[#6B7280] font-inter">记住我</span>
            </label>
            <button
              type="button"
              @click="handleForgotPassword"
              class="text-sm text-[#2563EB] hover:text-[#1D4ED8] font-inter transition-colors duration-200"
            >
              忘记密码？
            </button>
          </div>

          <!-- 用户协议 -->
          <UserAgreement v-model="agreedToTerms" />

          <AppButton
            type="submit"
            :disabled="!canSubmit"
            :loading="isLoading"
            class="w-full"
            variant="primary"
          >
            登录
          </AppButton>
        </form>

        <!-- 微信登录 -->
        <WeChatLogin v-else @success="handleWeChatLoginSuccess" />
      </div>

      <!-- 底部链接 -->
      <div class="text-center text-sm text-[#6B7280] font-inter">
        <p>
          还没有账号？
          <router-link
            to="/register"
            class="text-[#2563EB] hover:text-[#1D4ED8] transition-colors duration-200"
            >立即注册</router-link
          >
        </p>
      </div>
    </div>
  </div>
</template>
