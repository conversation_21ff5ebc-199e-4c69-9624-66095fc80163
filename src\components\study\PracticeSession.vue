<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import type { PracticeSession, Question, QuestionResult } from '@/types'
import QuestionCard from './QuestionCard.vue'
import { useNotificationStore } from '@/stores/notificationStore'

interface Props {
  session: PracticeSession
}

const props = defineProps<Props>()

const emit = defineEmits<{
  submitAnswer: [questionId: string, answer: string | string[]]
  complete: []
  exit: []
}>()

const router = useRouter()
const notificationStore = useNotificationStore()

// 状态管理
const currentQuestionIndex = ref(0)
const startTime = ref(Date.now())
const questionStartTime = ref(Date.now())
const questionResults = ref<Map<string, QuestionResult>>(new Map())
const showExplanation = ref(false)
const isCompleted = ref(false)

// 计时器
const elapsedTime = ref(0)
const timer = ref<NodeJS.Timeout>()

// 计算属性
const currentQuestion = computed(() => props.session.questions[currentQuestionIndex.value])
const totalQuestions = computed(() => props.session.questions.length)
const currentQuestionNumber = computed(() => currentQuestionIndex.value + 1)
const progress = computed(() => ((currentQuestionIndex.value + 1) / totalQuestions.value) * 100)

// 格式化时间
const formattedTime = computed(() => {
  const minutes = Math.floor(elapsedTime.value / 60)
  const seconds = elapsedTime.value % 60
  return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})

// 获取当前题目的用户答案
const currentUserAnswer = computed(() => {
  return props.session.answers[currentQuestion.value?.id] || undefined
})

// 检查当前题目是否已回答
const isCurrentQuestionAnswered = computed(() => {
  return currentQuestion.value?.id in props.session.answers
})

// 检查答案是否正确
const isCurrentAnswerCorrect = computed(() => {
  if (!isCurrentQuestionAnswered.value || !currentQuestion.value) return false
  
  const userAnswer = currentUserAnswer.value
  const correctAnswer = currentQuestion.value.correctAnswer
  
  if (Array.isArray(correctAnswer) && Array.isArray(userAnswer)) {
    return correctAnswer.length === userAnswer.length && 
           correctAnswer.every(answer => userAnswer.includes(answer))
  }
  
  return userAnswer === correctAnswer
})

// 生命周期
onMounted(() => {
  startTimer()
  questionStartTime.value = Date.now()
})

onUnmounted(() => {
  stopTimer()
})

// 开始计时
function startTimer() {
  timer.value = setInterval(() => {
    elapsedTime.value = Math.floor((Date.now() - startTime.value) / 1000)
  }, 1000)
}

// 停止计时
function stopTimer() {
  if (timer.value) {
    clearInterval(timer.value)
    timer.value = undefined
  }
}

// 处理答案提交
function handleAnswer(answer: string | string[]) {
  if (!currentQuestion.value) return
  
  const questionId = currentQuestion.value.id
  const timeSpent = Math.floor((Date.now() - questionStartTime.value) / 1000)
  
  // 记录题目结果
  const result: QuestionResult = {
    question: currentQuestion.value,
    userAnswer: answer,
    correctAnswer: currentQuestion.value.correctAnswer,
    isCorrect: isAnswerCorrect(answer, currentQuestion.value.correctAnswer),
    timeSpent
  }
  
  questionResults.value.set(questionId, result)
  
  // 提交答案
  emit('submitAnswer', questionId, answer)
  
  // 显示解析
  showExplanation.value = true
  
  // 3秒后自动进入下一题（如果不是最后一题）
  if (currentQuestionIndex.value < totalQuestions.value - 1) {
    setTimeout(() => {
      nextQuestion()
    }, 3000)
  }
}

// 检查答案是否正确
function isAnswerCorrect(userAnswer: string | string[], correctAnswer: string | string[]): boolean {
  if (Array.isArray(correctAnswer) && Array.isArray(userAnswer)) {
    return correctAnswer.length === userAnswer.length && 
           correctAnswer.every(answer => userAnswer.includes(answer))
  }
  return userAnswer === correctAnswer
}

// 下一题
function nextQuestion() {
  if (currentQuestionIndex.value < totalQuestions.value - 1) {
    currentQuestionIndex.value++
    showExplanation.value = false
    questionStartTime.value = Date.now()
  } else {
    completePractice()
  }
}

// 上一题
function previousQuestion() {
  if (currentQuestionIndex.value > 0) {
    currentQuestionIndex.value--
    showExplanation.value = isCurrentQuestionAnswered.value
    questionStartTime.value = Date.now()
  }
}

// 完成练习
function completePractice() {
  isCompleted.value = true
  stopTimer()
  emit('complete')
}

// 退出练习
function exitPractice() {
  if (Object.keys(props.session.answers).length > 0) {
    if (confirm('您还有未完成的练习，确定要退出吗？')) {
      emit('exit')
    }
  } else {
    emit('exit')
  }
}

// 跳转到指定题目
function goToQuestion(index: number) {
  if (index >= 0 && index < totalQuestions.value) {
    currentQuestionIndex.value = index
    showExplanation.value = props.session.questions[index].id in props.session.answers
    questionStartTime.value = Date.now()
  }
}

// 获取题目状态
function getQuestionStatus(index: number): 'answered' | 'current' | 'unanswered' {
  if (index === currentQuestionIndex.value) return 'current'
  if (props.session.questions[index].id in props.session.answers) return 'answered'
  return 'unanswered'
}
</script>

<template>
  <div class="min-h-screen bg-gray-50">
    <!-- 顶部导航栏 -->
    <div class="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-10">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex items-center justify-between h-16">
          <!-- 左侧信息 -->
          <div class="flex items-center space-x-4">
            <button
              @click="exitPractice"
              class="text-gray-600 hover:text-gray-800 transition-colors"
            >
              ← 退出练习
            </button>
            <div class="text-sm text-gray-600">
              题库练习
            </div>
          </div>

          <!-- 中间进度 -->
          <div class="flex-1 max-w-md mx-8">
            <div class="flex items-center justify-between text-sm text-gray-600 mb-1">
              <span>进度</span>
              <span>{{ currentQuestionNumber }}/{{ totalQuestions }}</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div 
                class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                :style="{ width: `${progress}%` }"
              ></div>
            </div>
          </div>

          <!-- 右侧时间 -->
          <div class="flex items-center space-x-4">
            <div class="text-sm text-gray-600">
              用时: {{ formattedTime }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="grid grid-cols-1 lg:grid-cols-4 gap-8">
        <!-- 题目导航 -->
        <div class="lg:col-span-1">
          <div class="bg-white rounded-lg shadow-sm p-4 sticky top-24">
            <h3 class="font-medium text-gray-900 mb-4">题目导航</h3>
            <div class="grid grid-cols-5 lg:grid-cols-4 gap-2">
              <button
                v-for="(question, index) in session.questions"
                :key="question.id"
                @click="goToQuestion(index)"
                class="w-8 h-8 rounded text-sm font-medium transition-colors"
                :class="{
                  'bg-blue-600 text-white': getQuestionStatus(index) === 'current',
                  'bg-green-100 text-green-800': getQuestionStatus(index) === 'answered',
                  'bg-gray-100 text-gray-600': getQuestionStatus(index) === 'unanswered'
                }"
              >
                {{ index + 1 }}
              </button>
            </div>
            
            <!-- 图例 -->
            <div class="mt-4 space-y-2 text-xs">
              <div class="flex items-center">
                <div class="w-3 h-3 bg-blue-600 rounded mr-2"></div>
                <span class="text-gray-600">当前题目</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-green-100 rounded mr-2"></div>
                <span class="text-gray-600">已答题目</span>
              </div>
              <div class="flex items-center">
                <div class="w-3 h-3 bg-gray-100 rounded mr-2"></div>
                <span class="text-gray-600">未答题目</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 题目内容 -->
        <div class="lg:col-span-3">
          <QuestionCard
            v-if="currentQuestion"
            :question="currentQuestion"
            :question-number="currentQuestionNumber"
            :total-questions="totalQuestions"
            :show-explanation="showExplanation"
            :user-answer="currentUserAnswer"
            :is-answered="isCurrentQuestionAnswered"
            :is-correct="isCurrentAnswerCorrect"
            :disabled="showExplanation"
            @answer="handleAnswer"
            @next="nextQuestion"
            @previous="previousQuestion"
          />
        </div>
      </div>
    </div>
  </div>
</template>
