<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { useNotificationStore } from '@/stores/notificationStore'
import AppButton from '@/components/ui/AppButton.vue'

interface Props {
  onSuccess?: () => void
  onError?: (error: string) => void
}

const props = defineProps<Props>()

const router = useRouter()
const authStore = useAuthStore()
const notificationStore = useNotificationStore()

const qrCodeUrl = ref<string>('')
const isLoading = ref(false)
const isPolling = ref(false)
const pollingTimer = ref<number | null>(null)
const qrCodeExpired = ref(false)
const scanStatus = ref<'waiting' | 'scanned' | 'confirmed' | 'expired'>('waiting')

// 生成微信登录二维码
async function generateQRCode() {
  isLoading.value = true
  qrCodeExpired.value = false
  scanStatus.value = 'waiting'
  
  try {
    // 模拟生成二维码API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟二维码URL
    qrCodeUrl.value = `https://api.weixin.qq.com/qrcode?scene=login_${Date.now()}`
    
    // 开始轮询扫码状态
    startPolling()
    
    // 设置二维码过期时间（5分钟）
    setTimeout(() => {
      if (scanStatus.value === 'waiting') {
        qrCodeExpired.value = true
        scanStatus.value = 'expired'
        stopPolling()
      }
    }, 5 * 60 * 1000)
    
  } catch (error) {
    notificationStore.error('生成失败', '无法生成微信登录二维码，请稍后重试')
    props.onError?.('Failed to generate QR code')
  } finally {
    isLoading.value = false
  }
}

// 开始轮询扫码状态
function startPolling() {
  if (isPolling.value) return
  
  isPolling.value = true
  pollingTimer.value = window.setInterval(async () => {
    try {
      // 模拟检查扫码状态API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      
      // 模拟不同的扫码状态
      const random = Math.random()
      if (random < 0.1 && scanStatus.value === 'waiting') {
        scanStatus.value = 'scanned'
        notificationStore.info('扫码成功', '请在手机上确认登录')
      } else if (random < 0.05 && scanStatus.value === 'scanned') {
        scanStatus.value = 'confirmed'
        await handleLoginSuccess()
      }
      
    } catch (error) {
      console.error('Polling error:', error)
    }
  }, 2000)
}

// 停止轮询
function stopPolling() {
  if (pollingTimer.value) {
    clearInterval(pollingTimer.value)
    pollingTimer.value = null
  }
  isPolling.value = false
}

// 处理登录成功
async function handleLoginSuccess() {
  stopPolling()
  
  try {
    // 模拟微信登录成功后的用户信息
    const mockUser = {
      id: 'wx_' + Date.now(),
      username: 'wechat_user',
      realName: '微信用户',
      workUnit: '某疾控中心',
      position: '医师',
      permissions: ['info:read', 'study:read', 'exam:read', 'profile:read'],
      email: '',
      phone: '',
      idCard: '',
      verificationStatus: 'unverified' as const,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
    authStore.setAuth({
      token: 'mock-wechat-jwt-token',
      user: mockUser,
      expiresIn: 3600
    })
    
    notificationStore.success('登录成功', '欢迎使用疾控医护任职资格考试系统')
    props.onSuccess?.()
    router.push('/')
    
  } catch (error) {
    notificationStore.error('登录失败', '微信登录失败，请重试')
    props.onError?.('WeChat login failed')
  }
}

// 刷新二维码
function refreshQRCode() {
  stopPolling()
  generateQRCode()
}

// 获取状态文本
function getStatusText() {
  switch (scanStatus.value) {
    case 'waiting':
      return '请使用微信扫描二维码'
    case 'scanned':
      return '扫描成功，请在手机上确认'
    case 'confirmed':
      return '确认成功，正在登录...'
    case 'expired':
      return '二维码已过期，请刷新'
    default:
      return '请使用微信扫描二维码'
  }
}

// 获取状态图标
function getStatusIcon() {
  switch (scanStatus.value) {
    case 'waiting':
      return '📱'
    case 'scanned':
      return '✅'
    case 'confirmed':
      return '🎉'
    case 'expired':
      return '⏰'
    default:
      return '📱'
  }
}

onMounted(() => {
  generateQRCode()
})

onUnmounted(() => {
  stopPolling()
})
</script>

<template>
  <div class="bg-white rounded-lg p-6 text-center">
    <h3 class="text-lg font-semibold text-[#111827] mb-4 font-inter">微信扫码登录</h3>
    
    <!-- 二维码区域 -->
    <div class="relative mb-4">
      <div v-if="isLoading" class="w-48 h-48 mx-auto bg-[#F8F9FA] rounded-lg flex items-center justify-center">
        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2563EB]"></div>
      </div>
      
      <div v-else-if="qrCodeUrl && !qrCodeExpired" class="w-48 h-48 mx-auto bg-[#F8F9FA] rounded-lg flex items-center justify-center relative">
        <!-- 模拟二维码 -->
        <div class="w-40 h-40 bg-black rounded-lg flex items-center justify-center">
          <div class="text-white text-xs font-inter">QR Code</div>
        </div>
        
        <!-- 扫码状态覆盖层 -->
        <div v-if="scanStatus !== 'waiting'" class="absolute inset-0 bg-black bg-opacity-75 rounded-lg flex items-center justify-center">
          <div class="text-white text-center">
            <div class="text-2xl mb-2">{{ getStatusIcon() }}</div>
            <div class="text-sm font-inter">{{ getStatusText() }}</div>
          </div>
        </div>
      </div>
      
      <div v-else class="w-48 h-48 mx-auto bg-[#F8F9FA] rounded-lg flex items-center justify-center">
        <div class="text-center text-[#6B7280]">
          <div class="text-2xl mb-2">⏰</div>
          <div class="text-sm font-inter">二维码已过期</div>
        </div>
      </div>
    </div>
    
    <!-- 状态文本 -->
    <p class="text-sm text-[#6B7280] mb-4 font-inter">{{ getStatusText() }}</p>
    
    <!-- 刷新按钮 -->
    <AppButton
      v-if="qrCodeExpired || scanStatus === 'expired'"
      variant="outline"
      size="sm"
      @click="refreshQRCode"
      :loading="isLoading"
    >
      刷新二维码
    </AppButton>
    
    <!-- 提示信息 -->
    <div class="mt-4 p-3 bg-[#EFF6FF] rounded-lg border border-[#DBEAFE]">
      <p class="text-xs text-[#1E40AF] font-inter">
        💡 使用微信扫描二维码，在手机上确认登录即可快速进入系统
      </p>
    </div>
  </div>
</template>
