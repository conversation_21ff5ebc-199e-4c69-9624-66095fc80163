<script setup lang="ts">
import { ref, watch } from 'vue'
import type { Exam } from '@/types'

interface Props {
  filters: {
    type: Exam['type'] | ''
    status: Exam['status'] | ''
    keyword: string
  }
  loading?: boolean
}

interface Emits {
  update: [filters: Props['filters']]
  reset: []
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<Emits>()

// 本地状态
const localFilters = ref({ ...props.filters })

// 监听外部filters变化
watch(() => props.filters, (newFilters) => {
  localFilters.value = { ...newFilters }
}, { deep: true })

// 考试类型选项
const typeOptions = [
  { value: '', label: '全部类型' },
  { value: 'online', label: '线上考试' },
  { value: 'offline', label: '线下考试' }
]

// 考试状态选项
const statusOptions = [
  { value: '', label: '全部状态' },
  { value: 'pending', label: '即将开始' },
  { value: 'ongoing', label: '进行中' },
  { value: 'completed', label: '已结束' },
  { value: 'cancelled', label: '已取消' }
]

// 快速筛选选项
const quickFilters = [
  { 
    label: '可报名', 
    filters: { type: '', status: 'ongoing', keyword: '' },
    icon: '📝'
  },
  { 
    label: '线上考试', 
    filters: { type: 'online' as const, status: '', keyword: '' },
    icon: '💻'
  },
  { 
    label: '线下考试', 
    filters: { type: 'offline' as const, status: '', keyword: '' },
    icon: '🏢'
  },
  { 
    label: '已结束', 
    filters: { type: '', status: 'completed' as const, keyword: '' },
    icon: '✅'
  }
]

// 处理筛选变化
function handleFilterChange() {
  emit('update', { ...localFilters.value })
}

// 处理快速筛选
function handleQuickFilter(quickFilter: typeof quickFilters[0]) {
  localFilters.value = { ...quickFilter.filters }
  handleFilterChange()
}

// 重置筛选
function handleReset() {
  localFilters.value = {
    type: '',
    status: '',
    keyword: ''
  }
  emit('reset')
}

// 检查是否有活跃的筛选条件
function hasActiveFilters(): boolean {
  return !!(localFilters.value.type || 
           localFilters.value.status || 
           localFilters.value.keyword)
}
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
    <!-- 标题 -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-lg font-semibold text-gray-900">筛选考试</h3>
      <button
        v-if="hasActiveFilters()"
        @click="handleReset"
        class="text-sm text-gray-500 hover:text-gray-700 transition-colors"
        :disabled="loading"
      >
        清除筛选
      </button>
    </div>

    <!-- 快速筛选 -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">快速筛选</h4>
      <div class="flex flex-wrap gap-2">
        <button
          v-for="quickFilter in quickFilters"
          :key="quickFilter.label"
          @click="handleQuickFilter(quickFilter)"
          class="inline-flex items-center px-3 py-2 text-sm font-medium rounded-lg border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 transition-colors"
          :disabled="loading"
        >
          <span class="mr-2">{{ quickFilter.icon }}</span>
          {{ quickFilter.label }}
        </button>
      </div>
    </div>

    <!-- 详细筛选 -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
      <!-- 关键词搜索 -->
      <div>
        <label for="keyword" class="block text-sm font-medium text-gray-700 mb-2">
          关键词搜索
        </label>
        <div class="relative">
          <input
            id="keyword"
            v-model="localFilters.keyword"
            type="text"
            placeholder="搜索考试名称..."
            class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
            :disabled="loading"
            @input="handleFilterChange"
          >
          <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
            <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
        </div>
      </div>

      <!-- 考试类型 -->
      <div>
        <label for="type" class="block text-sm font-medium text-gray-700 mb-2">
          考试类型
        </label>
        <select
          id="type"
          v-model="localFilters.type"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          :disabled="loading"
          @change="handleFilterChange"
        >
          <option
            v-for="option in typeOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>
      </div>

      <!-- 考试状态 -->
      <div>
        <label for="status" class="block text-sm font-medium text-gray-700 mb-2">
          考试状态
        </label>
        <select
          id="status"
          v-model="localFilters.status"
          class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors"
          :disabled="loading"
          @change="handleFilterChange"
        >
          <option
            v-for="option in statusOptions"
            :key="option.value"
            :value="option.value"
          >
            {{ option.label }}
          </option>
        </select>
      </div>
    </div>

    <!-- 活跃筛选条件显示 -->
    <div v-if="hasActiveFilters()" class="mt-4 pt-4 border-t border-gray-200">
      <div class="flex items-center flex-wrap gap-2">
        <span class="text-sm text-gray-500">当前筛选:</span>
        
        <span
          v-if="localFilters.keyword"
          class="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
        >
          关键词: {{ localFilters.keyword }}
        </span>
        
        <span
          v-if="localFilters.type"
          class="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full"
        >
          类型: {{ typeOptions.find(opt => opt.value === localFilters.type)?.label }}
        </span>
        
        <span
          v-if="localFilters.status"
          class="inline-flex items-center px-2 py-1 text-xs font-medium bg-purple-100 text-purple-800 rounded-full"
        >
          状态: {{ statusOptions.find(opt => opt.value === localFilters.status)?.label }}
        </span>
      </div>
    </div>
  </div>
</template>
