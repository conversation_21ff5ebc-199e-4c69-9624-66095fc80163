<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useStudy } from '@/composables/useStudy'
import { useNotificationStore } from '@/stores/notificationStore'
import type { QuestionCategory, PracticeConfig } from '@/types'
import CategoryList from '@/components/study/CategoryList.vue'
import PracticeSession from '@/components/study/PracticeSession.vue'
import PracticeResult from '@/components/study/PracticeResult.vue'
import AppCard from '@/components/ui/AppCard.vue'
import AppButton from '@/components/ui/AppButton.vue'

const router = useRouter()
const notificationStore = useNotificationStore()

// 使用学习中心 composable
const {
  categories,
  studyProgress,
  practiceStats,
  studyOverview,
  recommendedCategories,
  popularCategories,
  recentSessions,
  currentSession,
  currentResult,
  isLoading,
  isLoadingOverview,
  isLoadingRecommended,
  isLoadingPopular,
  isLoadingRecent,
  isInSession,
  hasResult,
  todayProgressPercent,
  isReachedDailyLimit,
  startPractice,
  submitAnswer,
  completePractice,
  exitPractice,
  restartPractice,
  reviewWrongQuestions,
  getCategoryProgress,
  refetchCategories,
  refetchOverview,
} = useStudy()

// 本地状态
const showPracticeConfig = ref(false)
const selectedCategory = ref<QuestionCategory | null>(null)
const practiceConfig = ref<PracticeConfig>({
  categoryId: '',
  questionCount: 10,
  difficulty: undefined,
  randomOrder: true,
  showExplanation: true,
})

// 计算属性
const currentView = computed(() => {
  if (hasResult.value) return 'result'
  if (isInSession.value) return 'practice'
  return 'categories'
})

// 生命周期
onMounted(() => {
  // 初始化数据
  refetchCategories()
  refetchOverview()
})

// 格式化方法
function formatDate(dateString: string) {
  return new Date(dateString).toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
  })
}

function getDifficultyColor(difficulty: string) {
  const colors = {
    easy: 'text-[#166534] bg-[#DCFCE7]',
    medium: 'text-[#92400E] bg-[#FEF3C7]',
    hard: 'text-[#991B1B] bg-[#FEE2E2]',
  }
  return colors[difficulty as keyof typeof colors] || colors.medium
}

function getDifficultyLabel(difficulty: string) {
  const labels = {
    easy: '简单',
    medium: '中等',
    hard: '困难',
  }
  return labels[difficulty as keyof typeof labels] || '中等'
}

// 处理分类选择
function handleSelectCategory(category: QuestionCategory) {
  selectedCategory.value = category
  practiceConfig.value.categoryId = category.id
  practiceConfig.value.questionCount = Math.min(10, category.questionCount)
  showPracticeConfig.value = true
}

// 处理复习错题
function handleReviewCategory(category: QuestionCategory) {
  reviewWrongQuestions(category.id)
}

// 开始练习
async function handleStartPractice() {
  if (!selectedCategory.value) return

  showPracticeConfig.value = false
  await startPractice(practiceConfig.value)
}

// 取消练习配置
function handleCancelConfig() {
  showPracticeConfig.value = false
  selectedCategory.value = null
}

// 提交答案
async function handleSubmitAnswer(questionId: string, answer: string | string[]) {
  await submitAnswer(questionId, answer)
}

// 完成练习
async function handleCompletePractice() {
  await completePractice()
}

// 退出练习
function handleExitPractice() {
  if (confirm('确定要退出当前练习吗？未完成的进度将会丢失。')) {
    exitPractice()
  }
}

// 重新开始练习
async function handleRestartPractice() {
  await restartPractice()
}

// 复习错题
async function handleReviewWrong() {
  if (currentResult.value) {
    await reviewWrongQuestions(currentResult.value.categoryId)
  }
}

// 返回分类列表
function handleBackToCategories() {
  exitPractice()
}
</script>

<template>
  <div class="min-h-screen bg-[#F8F9FA]">
    <!-- 分类列表视图 -->
    <div v-if="currentView === 'categories'" class="space-y-6">
      <!-- 页面头部 -->
      <AppCard padding="lg" shadow="sm">
        <div class="flex items-center justify-between">
          <div>
            <h1 class="text-3xl font-bold text-[#111827] font-inter">学习中心</h1>
            <p class="mt-2 text-[#6B7280] leading-relaxed font-inter">
              选择题库分类开始练习，提升您的专业技能
            </p>
          </div>

          <!-- 今日练习进度 -->
          <div v-if="studyOverview" class="flex items-center space-x-4">
            <div class="text-right">
              <div class="text-sm text-[#6B7280] font-medium font-inter">今日练习进度</div>
              <div class="text-lg font-semibold text-[#111827] font-inter">
                {{ studyOverview.todayPracticeCount }} / {{ studyOverview.todayPracticeLimit }}
              </div>
              <div class="w-32 bg-[#E5E7EB] rounded-full h-2 mt-1">
                <div
                  class="bg-[#2563EB] h-2 rounded-full transition-all duration-200"
                  :style="{ width: `${todayProgressPercent}%` }"
                ></div>
              </div>
            </div>
            <div class="text-6xl">📚</div>
          </div>

          <div v-else class="text-6xl">📚</div>
        </div>
      </AppCard>

      <!-- 快捷信息区域 -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        <!-- 推荐题库 -->
        <AppCard padding="lg" shadow="sm" hover>
          <h3
            class="text-lg font-semibold text-[#111827] mb-4 flex items-center space-x-2 font-inter"
          >
            <span>⭐</span>
            <span>推荐题库</span>
          </h3>

          <div v-if="isLoadingRecommended" class="space-y-3">
            <div v-for="i in 3" :key="i" class="animate-pulse">
              <div class="h-4 bg-[#E5E7EB] rounded w-3/4 mb-2"></div>
              <div class="h-3 bg-[#E5E7EB] rounded w-1/2"></div>
            </div>
          </div>

          <div
            v-else-if="recommendedCategories && recommendedCategories.length > 0"
            class="space-y-3"
          >
            <div
              v-for="category in recommendedCategories.slice(0, 5)"
              :key="category.id"
              class="cursor-pointer hover:bg-[#F8F9FA] p-3 rounded-lg transition-all duration-200 hover:shadow-sm"
              @click="handleSelectCategory(category)"
            >
              <h4 class="text-sm font-semibold text-[#111827] truncate font-inter">
                {{ category.name }}
              </h4>
              <div class="flex items-center space-x-2 mt-1 text-xs text-[#6B7280] font-inter">
                <span
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium font-inter"
                  :class="getDifficultyColor(category.difficulty)"
                >
                  {{ getDifficultyLabel(category.difficulty) }}
                </span>
                <span>•</span>
                <span>{{ category.questionCount }} 题</span>
              </div>
            </div>
          </div>

          <div v-else class="text-center text-[#6B7280] py-4">暂无推荐题库</div>
        </AppCard>

        <!-- 最近练习 -->
        <AppCard padding="lg" shadow="sm" hover>
          <h3
            class="text-lg font-semibold text-[#111827] mb-4 flex items-center space-x-2 font-inter"
          >
            <span>🕒</span>
            <span>最近练习</span>
          </h3>

          <div v-if="isLoadingRecent" class="space-y-3">
            <div v-for="i in 3" :key="i" class="animate-pulse">
              <div class="h-4 bg-[#E5E7EB] rounded w-3/4 mb-2"></div>
              <div class="h-3 bg-[#E5E7EB] rounded w-1/2"></div>
            </div>
          </div>

          <div v-else-if="recentSessions && recentSessions.length > 0" class="space-y-3">
            <div
              v-for="session in recentSessions.slice(0, 5)"
              :key="session.sessionId"
              class="cursor-pointer hover:bg-[#F8F9FA] p-3 rounded-lg transition-all duration-200 hover:shadow-sm"
            >
              <h4 class="text-sm font-semibold text-[#111827] truncate">
                {{ session.categoryName }}
              </h4>
              <div class="flex items-center space-x-2 mt-1 text-xs text-[#6B7280]">
                <span
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium"
                  :class="
                    session.accuracy >= 80
                      ? 'bg-[#DCFCE7] text-[#166534]'
                      : session.accuracy >= 60
                        ? 'bg-[#FEF3C7] text-[#92400E]'
                        : 'bg-[#FEE2E2] text-[#991B1B]'
                  "
                >
                  {{ session.accuracy.toFixed(1) }}%
                </span>
                <span>•</span>
                <span>{{ session.correctAnswers }}/{{ session.totalQuestions }}</span>
                <span>•</span>
                <span>{{ formatDate(session.completedAt) }}</span>
              </div>
            </div>
          </div>

          <div v-else class="text-center text-[#6B7280] py-4">暂无练习记录</div>
        </AppCard>
      </div>

      <!-- 学习统计 -->
      <AppCard v-if="practiceStats" padding="lg" shadow="sm">
        <h2 class="text-lg font-semibold text-[#111827] mb-4 font-inter">学习统计</h2>
        <div class="grid grid-cols-1 sm:grid-cols-4 gap-4">
          <div class="text-center">
            <div class="text-2xl font-bold text-[#2563EB] font-inter">
              {{ practiceStats.totalPracticed }}
            </div>
            <div class="text-sm text-[#6B7280] font-medium font-inter">总练习题数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-[#10B981] font-inter">
              {{ practiceStats.totalCorrect }}
            </div>
            <div class="text-sm text-[#6B7280] font-medium font-inter">答对题数</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-[#F59E0B] font-inter">
              {{ practiceStats.averageAccuracy.toFixed(1) }}%
            </div>
            <div class="text-sm text-[#6B7280] font-medium font-inter">平均正确率</div>
          </div>
          <div class="text-center">
            <div class="text-2xl font-bold text-[#8B5CF6] font-inter">
              {{ practiceStats.practiceStreak }}
            </div>
            <div class="text-sm text-[#6B7280] font-medium font-inter">连续练习天数</div>
          </div>
        </div>
      </AppCard>

      <!-- 题库分类列表 -->
      <CategoryList
        :categories="categories || []"
        :progress="studyProgress"
        :loading="isLoading"
        @select-category="handleSelectCategory"
        @review-category="handleReviewCategory"
        @refresh="refetchCategories"
      />

      <!-- 教材学习预留模块 -->
      <AppCard padding="lg" shadow="sm">
        <div class="flex items-center justify-between">
          <div>
            <h2 class="text-lg font-semibold text-[#111827] font-inter">教材学习</h2>
            <p class="mt-1 text-[#6B7280] font-inter">在线学习教材和课程资料</p>
          </div>
          <div class="text-4xl">📖</div>
        </div>
        <div class="mt-4 p-4 bg-[#EFF6FF] rounded-lg border border-[#DBEAFE]">
          <p class="text-[#1E40AF] font-medium font-inter">
            📚 教材学习功能正在建设中，敬请期待...
          </p>
        </div>
      </AppCard>
    </div>

    <!-- 练习会话视图 -->
    <PracticeSession
      v-else-if="currentView === 'practice' && currentSession"
      :session="currentSession"
      @submit-answer="handleSubmitAnswer"
      @complete="handleCompletePractice"
      @exit="handleExitPractice"
    />

    <!-- 练习结果视图 -->
    <PracticeResult
      v-else-if="currentView === 'result' && currentResult"
      :result="currentResult"
      @restart="handleRestartPractice"
      @review-wrong="handleReviewWrong"
      @back-to-categories="handleBackToCategories"
    />

    <!-- 练习配置弹窗 -->
    <div
      v-if="showPracticeConfig"
      class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      @click="handleCancelConfig"
    >
      <div class="bg-white rounded-lg shadow-xl p-6 w-full max-w-md mx-4" @click.stop>
        <h3 class="text-lg font-semibold text-[#111827] mb-4 font-inter">练习设置</h3>

        <div class="space-y-4">
          <!-- 题库信息 -->
          <div v-if="selectedCategory" class="p-3 bg-[#F8F9FA] rounded-lg">
            <div class="font-medium text-[#111827] font-inter">{{ selectedCategory.name }}</div>
            <div class="text-sm text-[#6B7280] font-inter">
              共 {{ selectedCategory.questionCount }} 题
            </div>
          </div>

          <!-- 题目数量 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">练习题数</label>
            <select
              v-model="practiceConfig.questionCount"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option :value="5">5 题</option>
              <option :value="10">10 题</option>
              <option :value="20">20 题</option>
              <option :value="50">50 题</option>
            </select>
          </div>

          <!-- 难度选择 -->
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-2">难度等级</label>
            <select
              v-model="practiceConfig.difficulty"
              class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option :value="undefined">全部难度</option>
              <option value="easy">简单</option>
              <option value="medium">中等</option>
              <option value="hard">困难</option>
            </select>
          </div>

          <!-- 其他选项 -->
          <div class="space-y-2">
            <label class="flex items-center">
              <input
                v-model="practiceConfig.randomOrder"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">随机题目顺序</span>
            </label>

            <label class="flex items-center">
              <input
                v-model="practiceConfig.showExplanation"
                type="checkbox"
                class="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
              <span class="ml-2 text-sm text-gray-700">显示答案解析</span>
            </label>
          </div>
        </div>

        <!-- 今日练习限制提示 -->
        <div
          v-if="isReachedDailyLimit"
          class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg"
        >
          <div class="flex items-center space-x-2">
            <span class="text-yellow-600">⚠️</span>
            <span class="text-sm text-yellow-800">今日练习次数已达上限，明天再来吧！</span>
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="flex space-x-3 mt-6">
          <AppButton variant="outline" size="md" block @click="handleCancelConfig">
            取消
          </AppButton>
          <AppButton
            variant="primary"
            size="md"
            block
            :disabled="isReachedDailyLimit"
            @click="handleStartPractice"
          >
            开始练习
          </AppButton>
        </div>
      </div>
    </div>
  </div>
</template>
