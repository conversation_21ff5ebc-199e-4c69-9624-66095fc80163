import { describe, it, expect, beforeEach, vi, afterEach } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useNotificationStore } from '../notificationStore'
import type { Notification } from '../notificationStore'

describe('notificationStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.useFakeTimers()
  })

  afterEach(() => {
    vi.useRealTimers()
  })

  describe('initial state', () => {
    it('should have empty notifications array initially', () => {
      const notificationStore = useNotificationStore()
      expect(notificationStore.notifications).toEqual([])
    })
  })

  describe('addNotification', () => {
    it('should add notification with default values', () => {
      const notificationStore = useNotificationStore()
      
      const id = notificationStore.addNotification({
        type: 'success',
        title: 'Test Success'
      })
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0]).toMatchObject({
        id,
        type: 'success',
        title: 'Test Success',
        duration: 5000,
        persistent: false
      })
    })

    it('should add notification with custom values', () => {
      const notificationStore = useNotificationStore()
      
      const id = notificationStore.addNotification({
        type: 'error',
        title: 'Test Error',
        message: 'Error message',
        duration: 10000,
        persistent: true
      })
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0]).toMatchObject({
        id,
        type: 'error',
        title: 'Test Error',
        message: 'Error message',
        duration: 10000,
        persistent: true
      })
    })

    it('should auto-remove non-persistent notifications after duration', () => {
      const notificationStore = useNotificationStore()
      
      const id = notificationStore.addNotification({
        type: 'info',
        title: 'Auto Remove',
        duration: 1000
      })
      
      expect(notificationStore.notifications).toHaveLength(1)
      
      // Fast-forward time
      vi.advanceTimersByTime(1000)
      
      expect(notificationStore.notifications).toHaveLength(0)
    })

    it('should not auto-remove persistent notifications', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.addNotification({
        type: 'warning',
        title: 'Persistent Warning',
        persistent: true,
        duration: 1000
      })
      
      expect(notificationStore.notifications).toHaveLength(1)
      
      // Fast-forward time
      vi.advanceTimersByTime(2000)
      
      expect(notificationStore.notifications).toHaveLength(1)
    })

    it('should not auto-remove notifications without duration', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.addNotification({
        type: 'info',
        title: 'No Duration',
        duration: 0
      })
      
      expect(notificationStore.notifications).toHaveLength(1)
      
      // Fast-forward time
      vi.advanceTimersByTime(10000)
      
      expect(notificationStore.notifications).toHaveLength(1)
    })
  })

  describe('removeNotification', () => {
    it('should remove notification by id', () => {
      const notificationStore = useNotificationStore()
      
      const id1 = notificationStore.addNotification({
        type: 'success',
        title: 'First'
      })
      
      const id2 = notificationStore.addNotification({
        type: 'error',
        title: 'Second'
      })
      
      expect(notificationStore.notifications).toHaveLength(2)
      
      notificationStore.removeNotification(id1)
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0].id).toBe(id2)
    })

    it('should handle removing non-existent notification', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.addNotification({
        type: 'info',
        title: 'Test'
      })
      
      expect(notificationStore.notifications).toHaveLength(1)
      
      notificationStore.removeNotification('non-existent-id')
      
      expect(notificationStore.notifications).toHaveLength(1)
    })
  })

  describe('clearAllNotifications', () => {
    it('should clear all notifications', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.addNotification({
        type: 'success',
        title: 'First'
      })
      
      notificationStore.addNotification({
        type: 'error',
        title: 'Second'
      })
      
      expect(notificationStore.notifications).toHaveLength(2)
      
      notificationStore.clearAllNotifications()
      
      expect(notificationStore.notifications).toHaveLength(0)
    })
  })

  describe('convenience methods', () => {
    it('should add success notification', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.success('Success Title', 'Success message')
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0]).toMatchObject({
        type: 'success',
        title: 'Success Title',
        message: 'Success message'
      })
    })

    it('should add error notification', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.error('Error Title', 'Error message')
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0]).toMatchObject({
        type: 'error',
        title: 'Error Title',
        message: 'Error message'
      })
    })

    it('should add warning notification', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.warning('Warning Title', 'Warning message')
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0]).toMatchObject({
        type: 'warning',
        title: 'Warning Title',
        message: 'Warning message'
      })
    })

    it('should add info notification', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.info('Info Title', 'Info message')
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0]).toMatchObject({
        type: 'info',
        title: 'Info Title',
        message: 'Info message'
      })
    })

    it('should handle convenience methods without message', () => {
      const notificationStore = useNotificationStore()
      
      notificationStore.success('Success Title')
      
      expect(notificationStore.notifications).toHaveLength(1)
      expect(notificationStore.notifications[0]).toMatchObject({
        type: 'success',
        title: 'Success Title'
      })
      expect(notificationStore.notifications[0].message).toBeUndefined()
    })
  })
})
