import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createP<PERSON>, setActivePinia } from 'pinia'
import { useAuthStore } from '../authStore'
import type { User, LoginResponse } from '@/types'

// Mock localStorage
const localStorageMock = {
  getItem: vi.fn(),
  setItem: vi.fn(),
  removeItem: vi.fn(),
  clear: vi.fn()
}

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock
})

const mockUser: User = {
  id: '1',
  username: 'testuser',
  email: '<EMAIL>',
  name: '测试用户',
  avatar: 'https://example.com/avatar.jpg',
  permissions: ['read', 'write'],
  roles: ['user']
}

const mockLoginResponse: LoginResponse = {
  token: 'mock-jwt-token',
  user: mockUser,
  expiresIn: 3600
}

describe('authStore', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
    vi.clearAllMocks()
    localStorageMock.getItem.mockReturnValue(null)
  })

  describe('initial state', () => {
    it('should have correct initial state when no stored data', () => {
      const authStore = useAuthStore()
      
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
      expect(authStore.isLoading).toBe(false)
      expect(authStore.isAuthenticated).toBe(false)
      expect(authStore.userPermissions).toEqual([])
    })

    it('should initialize from localStorage when data exists', () => {
      localStorageMock.getItem.mockImplementation((key: string) => {
        if (key === 'token') return 'stored-token'
        if (key === 'user') return JSON.stringify(mockUser)
        return null
      })

      const authStore = useAuthStore()
      authStore.initAuth()
      
      expect(authStore.token).toBe('stored-token')
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.isAuthenticated).toBe(true)
    })

    it('should clear auth when stored user data is invalid', () => {
      localStorageMock.getItem.mockImplementation((key: string) => {
        if (key === 'token') return 'stored-token'
        if (key === 'user') return 'invalid-json'
        return null
      })

      const authStore = useAuthStore()
      authStore.initAuth()
      
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('user')
    })
  })

  describe('setAuth', () => {
    it('should set authentication data correctly', () => {
      const authStore = useAuthStore()
      
      authStore.setAuth(mockLoginResponse)
      
      expect(authStore.token).toBe(mockLoginResponse.token)
      expect(authStore.user).toEqual(mockLoginResponse.user)
      expect(authStore.isAuthenticated).toBe(true)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('token', mockLoginResponse.token)
      expect(localStorageMock.setItem).toHaveBeenCalledWith('user', JSON.stringify(mockLoginResponse.user))
    })
  })

  describe('clearAuth', () => {
    it('should clear authentication data', () => {
      const authStore = useAuthStore()
      
      // Set auth first
      authStore.setAuth(mockLoginResponse)
      expect(authStore.isAuthenticated).toBe(true)
      
      // Clear auth
      authStore.clearAuth()
      
      expect(authStore.token).toBeNull()
      expect(authStore.user).toBeNull()
      expect(authStore.isAuthenticated).toBe(false)
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('token')
      expect(localStorageMock.removeItem).toHaveBeenCalledWith('user')
    })
  })

  describe('setUser', () => {
    it('should update user data', () => {
      const authStore = useAuthStore()
      
      authStore.setUser(mockUser)
      
      expect(authStore.user).toEqual(mockUser)
      expect(authStore.userPermissions).toEqual(mockUser.permissions)
    })
  })

  describe('setLoading', () => {
    it('should update loading state', () => {
      const authStore = useAuthStore()
      
      expect(authStore.isLoading).toBe(false)
      
      authStore.setLoading(true)
      expect(authStore.isLoading).toBe(true)
      
      authStore.setLoading(false)
      expect(authStore.isLoading).toBe(false)
    })
  })

  describe('permission methods', () => {
    beforeEach(() => {
      const authStore = useAuthStore()
      authStore.setUser(mockUser)
    })

    it('should check single permission correctly', () => {
      const authStore = useAuthStore()
      
      expect(authStore.hasPermission('read')).toBe(true)
      expect(authStore.hasPermission('write')).toBe(true)
      expect(authStore.hasPermission('admin')).toBe(false)
    })

    it('should check multiple permissions correctly', () => {
      const authStore = useAuthStore()
      
      expect(authStore.hasAnyPermission(['read', 'admin'])).toBe(true)
      expect(authStore.hasAnyPermission(['admin', 'super'])).toBe(false)
      expect(authStore.hasAnyPermission([])).toBe(false)
    })

    it('should check all permissions correctly', () => {
      const authStore = useAuthStore()
      
      expect(authStore.hasAllPermissions(['read', 'write'])).toBe(true)
      expect(authStore.hasAllPermissions(['read', 'admin'])).toBe(false)
      expect(authStore.hasAllPermissions([])).toBe(true)
    })
  })

  describe('computed properties', () => {
    it('should update isAuthenticated when token or user changes', () => {
      const authStore = useAuthStore()
      
      expect(authStore.isAuthenticated).toBe(false)
      
      // Set only token
      authStore.token = 'test-token'
      expect(authStore.isAuthenticated).toBe(false)
      
      // Set only user
      authStore.token = null
      authStore.setUser(mockUser)
      expect(authStore.isAuthenticated).toBe(false)
      
      // Set both token and user
      authStore.token = 'test-token'
      expect(authStore.isAuthenticated).toBe(true)
    })

    it('should update userPermissions when user changes', () => {
      const authStore = useAuthStore()
      
      expect(authStore.userPermissions).toEqual([])
      
      authStore.setUser(mockUser)
      expect(authStore.userPermissions).toEqual(['read', 'write'])
      
      authStore.setUser({ ...mockUser, permissions: ['admin'] })
      expect(authStore.userPermissions).toEqual(['admin'])
    })
  })
})
