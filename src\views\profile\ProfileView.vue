<script setup lang="ts">
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useProfile } from '@/composables/useProfile'
import { useAuthStore } from '@/stores/authStore'
import ProfileTabs from '@/components/profile/ProfileTabs.vue'
import PersonalInfo from '@/components/profile/PersonalInfo.vue'
import CertificateManagement from '@/components/profile/CertificateManagement.vue'
import GuestPrompt from '@/components/common/GuestPrompt.vue'

const route = useRoute()
const authStore = useAuthStore()

// 使用个人中心 composable
const {
  user,
  certificationStatus,
  profileStats,
  validCertificates,
  isLoading,
  refetchUser,
  refetchCertification,
  refetchStats,
  refetchCertificates,
} = useProfile()

// 计算属性
const currentTabComponent = computed(() => {
  const path = route.path
  if (path.includes('/certificates')) {
    return 'CertificateManagement'
  }
  return 'PersonalInfo'
})

const pageTitle = computed(() => {
  const path = route.path
  if (path.includes('/certificates')) {
    return '证书管理'
  }
  return '个人信息'
})

// 生命周期
onMounted(() => {
  // 初始化数据
  if (authStore.isAuthenticated) {
    refetchUser()
    refetchCertification()
    refetchStats()
    refetchCertificates()
  }
})
</script>

<template>
  <div class="space-y-6">
    <!-- 访客提示 -->
    <GuestPrompt
      v-if="!authStore.isAuthenticated"
      title="登录以访问个人中心"
      message="个人中心提供个人信息管理和证书管理功能。请先登录以查看和管理您的个人资料。"
    />
    <!-- 页面头部 -->
    <div class="bg-white rounded-lg shadow-sm p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold text-[#111827] font-inter">个人中心</h1>
          <p class="mt-2 text-[#6B7280] font-inter">管理您的个人信息和证书</p>
        </div>

        <!-- 用户头像和基本信息 -->
        <div v-if="user" class="flex items-center space-x-4">
          <div class="text-right">
            <div class="text-lg font-medium text-[#111827] font-inter">
              {{ user.realName || user.username }}
            </div>
            <div class="text-sm text-[#6B7280] font-inter">
              {{ user.workUnit || '未设置工作单位' }}
            </div>
            <div class="text-xs text-[#6B7280] font-inter">
              {{ user.position || '未设置职位' }}
            </div>
          </div>

          <div class="w-16 h-16 rounded-full overflow-hidden bg-gray-200">
            <img
              v-if="user.avatar"
              :src="user.avatar"
              :alt="user.realName || user.username"
              class="w-full h-full object-cover"
            />
            <div v-else class="w-full h-full flex items-center justify-center text-gray-400">
              <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                <path
                  fill-rule="evenodd"
                  d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                  clip-rule="evenodd"
                />
              </svg>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 快速统计卡片 -->
    <div v-if="profileStats" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-[#EFF6FF] rounded-lg">
            <svg
              class="w-6 h-6 text-[#2563EB]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
              />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-[#6B7280] font-inter">参加考试</p>
            <p class="text-2xl font-semibold text-[#111827] font-inter">
              {{ profileStats.totalExams }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-[#DCFCE7] rounded-lg">
            <svg
              class="w-6 h-6 text-[#10B981]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"
              />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-[#6B7280] font-inter">有效证书</p>
            <p class="text-2xl font-semibold text-[#111827] font-inter">
              {{ profileStats.validCertificates }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-[#F3E8FF] rounded-lg">
            <svg
              class="w-6 h-6 text-[#8B5CF6]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
              />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-[#6B7280] font-inter">练习题数</p>
            <p class="text-2xl font-semibold text-[#111827] font-inter">
              {{ profileStats.totalPracticeQuestions }}
            </p>
          </div>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow-sm p-4">
        <div class="flex items-center">
          <div class="p-2 bg-[#FEF3C7] rounded-lg">
            <svg
              class="w-6 h-6 text-[#F59E0B]"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M13 10V3L4 14h7v7l9-11h-7z"
              />
            </svg>
          </div>
          <div class="ml-4">
            <p class="text-sm font-medium text-gray-600">平均分数</p>
            <p class="text-2xl font-semibold text-gray-900">
              {{ profileStats.averageScore.toFixed(1) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- 认证状态提示 -->
    <div v-if="certificationStatus" class="bg-white rounded-lg shadow-sm p-4">
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-3">
          <div
            class="w-3 h-3 rounded-full"
            :class="{
              'bg-green-500': certificationStatus.status === 'approved',
              'bg-yellow-500': certificationStatus.status === 'pending',
              'bg-red-500': certificationStatus.status === 'rejected',
              'bg-gray-400': certificationStatus.status === 'not_submitted',
            }"
          ></div>
          <div>
            <span class="text-sm font-medium text-gray-900">认证状态: </span>
            <span
              class="text-sm font-medium"
              :class="{
                'text-green-600': certificationStatus.status === 'approved',
                'text-yellow-600': certificationStatus.status === 'pending',
                'text-red-600': certificationStatus.status === 'rejected',
                'text-gray-600': certificationStatus.status === 'not_submitted',
              }"
            >
              {{
                certificationStatus.status === 'approved'
                  ? '已认证'
                  : certificationStatus.status === 'pending'
                    ? '审核中'
                    : certificationStatus.status === 'rejected'
                      ? '审核未通过'
                      : '未提交'
              }}
            </span>
          </div>
        </div>

        <router-link
          v-if="
            certificationStatus.status === 'not_submitted' ||
            certificationStatus.status === 'rejected'
          "
          to="/profile/info"
          class="text-sm text-blue-600 hover:text-blue-800 transition-colors"
        >
          完善信息 →
        </router-link>
      </div>
    </div>

    <!-- 标签页内容 -->
    <ProfileTabs>
      <template #default="{ activeTab }">
        <!-- 个人信息标签页 -->
        <PersonalInfo v-if="activeTab === 'info'" />

        <!-- 证书管理标签页 -->
        <CertificateManagement v-else-if="activeTab === 'certificates'" />
      </template>
    </ProfileTabs>
  </div>
</template>
