import { ref, computed } from 'vue'
import { useQuery, useMutation, useQueryClient } from '@tanstack/vue-query'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/authStore'
import { useNotificationStore } from '@/stores/notificationStore'
import type { User, Certificate } from '@/types'
import * as profileApi from '@/api/profile'

export function useProfile() {
  const queryClient = useQueryClient()
  const router = useRouter()
  const authStore = useAuthStore()
  const notificationStore = useNotificationStore()

  // 状态管理
  const isEditing = ref(false)
  const editForm = ref<Partial<User>>({})

  // 获取当前用户信息
  const {
    data: currentUser,
    isLoading: isLoadingUser,
    error: userError,
    refetch: refetchUser
  } = useQuery({
    queryKey: ['profile', 'me'],
    queryFn: profileApi.getCurrentUser,
    staleTime: 1000 * 60 * 5, // 5分钟
    enabled: authStore.isAuthenticated
  })

  // 获取认证状态
  const {
    data: certificationStatus,
    isLoading: isLoadingCertification,
    refetch: refetchCertification
  } = useQuery({
    queryKey: ['profile', 'certification-status'],
    queryFn: profileApi.getCertificationStatus,
    staleTime: 1000 * 60 * 5, // 5分钟
    enabled: authStore.isAuthenticated
  })

  // 获取个人统计
  const {
    data: profileStats,
    isLoading: isLoadingStats,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['profile', 'stats'],
    queryFn: profileApi.getProfileStats,
    staleTime: 1000 * 60 * 10, // 10分钟
    enabled: authStore.isAuthenticated
  })

  // 获取证书列表
  const {
    data: certificatesData,
    isLoading: isLoadingCertificates,
    refetch: refetchCertificates
  } = useQuery({
    queryKey: ['profile', 'certificates'],
    queryFn: () => profileApi.getUserCertificates(),
    staleTime: 1000 * 60 * 5, // 5分钟
    enabled: authStore.isAuthenticated
  })

  // 更新个人信息
  const updateProfileMutation = useMutation({
    mutationFn: profileApi.updateProfile,
    onSuccess: (updatedUser) => {
      // 更新认证store中的用户信息
      authStore.setAuth({
        token: authStore.token!,
        user: updatedUser,
        expiresIn: 3600
      })
      
      // 更新查询缓存
      queryClient.setQueryData(['profile', 'me'], updatedUser)
      
      notificationStore.success('更新成功', '个人信息已更新')
      isEditing.value = false
    },
    onError: (error) => {
      notificationStore.error('更新失败', '个人信息更新失败，请稍后重试')
      console.error('Update profile failed:', error)
    }
  })

  // 上传头像
  const uploadAvatarMutation = useMutation({
    mutationFn: profileApi.uploadAvatar,
    onSuccess: (result) => {
      // 更新用户头像
      if (authStore.user) {
        const updatedUser = { ...authStore.user, avatar: result.url }
        authStore.setAuth({
          token: authStore.token!,
          user: updatedUser,
          expiresIn: 3600
        })
        queryClient.setQueryData(['profile', 'me'], updatedUser)
      }
      
      notificationStore.success('上传成功', '头像已更新')
    },
    onError: (error) => {
      notificationStore.error('上传失败', '头像上传失败，请稍后重试')
      console.error('Upload avatar failed:', error)
    }
  })

  // 提交认证申请
  const submitCertificationMutation = useMutation({
    mutationFn: profileApi.submitCertification,
    onSuccess: () => {
      notificationStore.success('提交成功', '认证申请已提交，请等待审核')
      refetchCertification()
      isEditing.value = false
    },
    onError: (error) => {
      notificationStore.error('提交失败', '认证申请提交失败，请稍后重试')
      console.error('Submit certification failed:', error)
    }
  })

  // 下载证书
  const downloadCertificateMutation = useMutation({
    mutationFn: profileApi.downloadCertificate,
    onSuccess: (blob, certificateId) => {
      // 创建下载链接
      const url = window.URL.createObjectURL(blob)
      const link = document.createElement('a')
      link.href = url
      link.download = `certificate-${certificateId}.pdf`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      window.URL.revokeObjectURL(url)
      
      notificationStore.success('下载成功', '证书已开始下载')
    },
    onError: (error) => {
      notificationStore.error('下载失败', '证书下载失败，请稍后重试')
      console.error('Download certificate failed:', error)
    }
  })

  // 修改密码
  const changePasswordMutation = useMutation({
    mutationFn: profileApi.changePassword,
    onSuccess: () => {
      notificationStore.success('修改成功', '密码已修改，请重新登录')
      // 清除认证状态，要求重新登录
      authStore.clearAuth()
      router.push('/login')
    },
    onError: (error) => {
      notificationStore.error('修改失败', '密码修改失败，请检查当前密码是否正确')
      console.error('Change password failed:', error)
    }
  })

  // 计算属性
  const user = computed(() => currentUser.value || authStore.user)
  const certificates = computed(() => certificatesData.value?.items || [])
  const validCertificates = computed(() => 
    certificates.value.filter(cert => cert.status === 'valid')
  )

  const isLoading = computed(() => 
    isLoadingUser.value || 
    isLoadingCertification.value || 
    isLoadingStats.value || 
    isLoadingCertificates.value
  )

  const hasError = computed(() => !!userError.value)

  const canEdit = computed(() => {
    return !certificationStatus.value || 
           certificationStatus.value.status === 'not_submitted' || 
           certificationStatus.value.status === 'rejected'
  })

  // 方法
  function startEdit() {
    if (!canEdit.value) {
      notificationStore.warning('无法编辑', '已认证用户的信息无法修改')
      return
    }
    
    editForm.value = { ...user.value }
    isEditing.value = true
  }

  function cancelEdit() {
    editForm.value = { ...user.value }
    isEditing.value = false
  }

  async function saveProfile(data: Partial<User>) {
    try {
      await updateProfileMutation.mutateAsync(data)
    } catch (error) {
      // 错误已在 mutation 中处理
    }
  }

  async function uploadAvatar(file: File) {
    try {
      await uploadAvatarMutation.mutateAsync(file)
    } catch (error) {
      // 错误已在 mutation 中处理
    }
  }

  async function submitCertification(data: Parameters<typeof profileApi.submitCertification>[0]) {
    try {
      await submitCertificationMutation.mutateAsync(data)
    } catch (error) {
      // 错误已在 mutation 中处理
    }
  }

  async function downloadCertificate(certificateId: string) {
    try {
      await downloadCertificateMutation.mutateAsync(certificateId)
    } catch (error) {
      // 错误已在 mutation 中处理
    }
  }

  async function changePassword(data: Parameters<typeof profileApi.changePassword>[0]) {
    try {
      await changePasswordMutation.mutateAsync(data)
    } catch (error) {
      // 错误已在 mutation 中处理
    }
  }

  // 获取证书详情查询
  function getCertificateDetailQuery(certificateId: string) {
    return useQuery({
      queryKey: ['profile', 'certificate', certificateId],
      queryFn: () => profileApi.getCertificateDetail(certificateId),
      staleTime: 1000 * 60 * 10, // 10分钟
      enabled: !!certificateId
    })
  }

  // 获取活动日志查询
  function getActivityLogsQuery(params?: Parameters<typeof profileApi.getActivityLogs>[0]) {
    return useQuery({
      queryKey: ['profile', 'activity-logs', params],
      queryFn: () => profileApi.getActivityLogs(params),
      staleTime: 1000 * 60 * 5, // 5分钟
    })
  }

  return {
    // 数据
    user,
    currentUser,
    certificationStatus,
    profileStats,
    certificates,
    validCertificates,
    editForm,
    
    // 状态
    isEditing,
    isLoadingUser,
    isLoadingCertification,
    isLoadingStats,
    isLoadingCertificates,
    isLoading,
    hasError,
    canEdit,
    
    // 查询方法
    getCertificateDetailQuery,
    getActivityLogsQuery,
    
    // 操作方法
    startEdit,
    cancelEdit,
    saveProfile,
    uploadAvatar,
    submitCertification,
    downloadCertificate,
    changePassword,
    
    // 刷新方法
    refetchUser,
    refetchCertification,
    refetchStats,
    refetchCertificates
  }
}
