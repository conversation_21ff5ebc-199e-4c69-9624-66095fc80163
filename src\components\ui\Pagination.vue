<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  currentPage: number
  totalPages: number
  totalItems: number
  pageSize: number
  loading?: boolean
  showSizeChanger?: boolean
  showQuickJumper?: boolean
  showTotal?: boolean
}

interface Emits {
  change: [page: number]
  sizeChange: [pageSize: number]
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  showSizeChanger: true,
  showQuickJumper: false,
  showTotal: true
})

const emit = defineEmits<Emits>()

// 页码大小选项
const pageSizeOptions = [10, 20, 50, 100]

// 计算显示的页码范围
const pageRange = computed(() => {
  const { currentPage, totalPages } = props
  const delta = 2 // 当前页前后显示的页数
  const range = []
  const rangeWithDots = []

  // 计算显示范围
  const start = Math.max(1, currentPage - delta)
  const end = Math.min(totalPages, currentPage + delta)

  for (let i = start; i <= end; i++) {
    range.push(i)
  }

  // 添加省略号和边界页码
  if (start > 1) {
    rangeWithDots.push(1)
    if (start > 2) {
      rangeWithDots.push('...')
    }
  }

  rangeWithDots.push(...range)

  if (end < totalPages) {
    if (end < totalPages - 1) {
      rangeWithDots.push('...')
    }
    rangeWithDots.push(totalPages)
  }

  return rangeWithDots
})

// 计算显示的数据范围
const dataRange = computed(() => {
  const { currentPage, pageSize, totalItems } = props
  const start = (currentPage - 1) * pageSize + 1
  const end = Math.min(currentPage * pageSize, totalItems)
  return { start, end }
})

// 处理页码变化
function handlePageChange(page: number | string) {
  if (typeof page === 'number' && page !== props.currentPage && !props.loading) {
    emit('change', page)
  }
}

// 处理页码大小变化
function handleSizeChange(event: Event) {
  const target = event.target as HTMLSelectElement
  const newSize = parseInt(target.value)
  if (newSize !== props.pageSize && !props.loading) {
    emit('sizeChange', newSize)
  }
}

// 上一页
function handlePrevious() {
  if (props.currentPage > 1) {
    handlePageChange(props.currentPage - 1)
  }
}

// 下一页
function handleNext() {
  if (props.currentPage < props.totalPages) {
    handlePageChange(props.currentPage + 1)
  }
}

// 检查是否可以点击
function canClick(page: number | string): boolean {
  return typeof page === 'number' && page !== props.currentPage && !props.loading
}

// 检查是否是当前页
function isCurrentPage(page: number | string): boolean {
  return page === props.currentPage
}
</script>

<template>
  <div v-if="totalPages > 1" class="flex flex-col sm:flex-row items-center justify-between gap-4 py-4">
    <!-- 总数信息 -->
    <div v-if="showTotal" class="text-sm text-gray-700">
      显示第 {{ dataRange.start }} - {{ dataRange.end }} 条，共 {{ totalItems }} 条记录
    </div>

    <!-- 分页控件 -->
    <div class="flex items-center space-x-2">
      <!-- 上一页 -->
      <button
        @click="handlePrevious"
        :disabled="currentPage <= 1 || loading"
        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
        </svg>
      </button>

      <!-- 页码 -->
      <div class="flex items-center space-x-1">
        <button
          v-for="page in pageRange"
          :key="page"
          @click="handlePageChange(page)"
          :disabled="!canClick(page)"
          class="px-3 py-2 text-sm font-medium rounded-lg transition-colors"
          :class="{
            'bg-blue-600 text-white': isCurrentPage(page),
            'text-gray-500 bg-white border border-gray-300 hover:bg-gray-50 hover:text-gray-700': !isCurrentPage(page) && typeof page === 'number',
            'text-gray-400 cursor-default': page === '...',
            'disabled:opacity-50 disabled:cursor-not-allowed': loading
          }"
        >
          {{ page }}
        </button>
      </div>

      <!-- 下一页 -->
      <button
        @click="handleNext"
        :disabled="currentPage >= totalPages || loading"
        class="px-3 py-2 text-sm font-medium text-gray-500 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 hover:text-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
      >
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
        </svg>
      </button>
    </div>

    <!-- 页码大小选择器 -->
    <div v-if="showSizeChanger" class="flex items-center space-x-2">
      <span class="text-sm text-gray-700">每页显示</span>
      <select
        :value="pageSize"
        @change="handleSizeChange"
        :disabled="loading"
        class="px-2 py-1 text-sm border border-gray-300 rounded focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        <option
          v-for="size in pageSizeOptions"
          :key="size"
          :value="size"
        >
          {{ size }} 条
        </option>
      </select>
    </div>
  </div>
</template>
