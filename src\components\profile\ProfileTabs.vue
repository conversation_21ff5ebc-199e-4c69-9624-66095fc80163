<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

interface Tab {
  id: string
  name: string
  icon: string
  path: string
  component: string
}

const route = useRoute()
const router = useRouter()

// 定义标签页
const tabs: Tab[] = [
  {
    id: 'info',
    name: '个人信息',
    icon: 'user',
    path: '/profile/info',
    component: 'PersonalInfo'
  },
  {
    id: 'certificates',
    name: '证书管理',
    icon: 'certificate',
    path: '/profile/certificates',
    component: 'CertificateManagement'
  }
]

// 当前激活的标签页
const activeTab = ref('info')

// 计算属性
const currentTab = computed(() => {
  return tabs.find(tab => tab.id === activeTab.value) || tabs[0]
})

// 监听路由变化
watch(() => route.path, (newPath) => {
  const tab = tabs.find(t => t.path === newPath)
  if (tab) {
    activeTab.value = tab.id
  }
}, { immediate: true })

// 生命周期
onMounted(() => {
  // 根据当前路由设置激活标签页
  const currentPath = route.path
  const tab = tabs.find(t => t.path === currentPath)
  if (tab) {
    activeTab.value = tab.id
  } else {
    // 如果当前路径不匹配任何标签页，重定向到默认标签页
    router.replace('/profile/info')
  }
})

// 方法
function switchTab(tabId: string) {
  const tab = tabs.find(t => t.id === tabId)
  if (tab) {
    activeTab.value = tabId
    router.push(tab.path)
  }
}

// 获取图标SVG
function getIconSvg(iconName: string): string {
  const icons = {
    user: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />`,
    certificate: `<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z" />`
  }
  return icons[iconName as keyof typeof icons] || icons.user
}
</script>

<template>
  <div class="bg-white rounded-lg shadow-sm overflow-hidden">
    <!-- 标签页导航 -->
    <div class="border-b border-gray-200">
      <nav class="flex space-x-8 px-6" aria-label="Tabs">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="switchTab(tab.id)"
          class="group inline-flex items-center py-4 px-1 border-b-2 font-medium text-sm transition-colors duration-200"
          :class="{
            'border-blue-500 text-blue-600': activeTab === tab.id,
            'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== tab.id
          }"
          :aria-current="activeTab === tab.id ? 'page' : undefined"
        >
          <!-- 图标 -->
          <svg
            class="mr-2 h-5 w-5 transition-colors duration-200"
            :class="{
              'text-blue-500': activeTab === tab.id,
              'text-gray-400 group-hover:text-gray-500': activeTab !== tab.id
            }"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            v-html="getIconSvg(tab.icon)"
          ></svg>
          
          <!-- 标签名称 -->
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- 标签页内容 -->
    <div class="p-6">
      <div class="min-h-[400px]">
        <slot :current-tab="currentTab" :active-tab="activeTab" />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 确保标签页切换动画流畅 */
.tab-content-enter-active,
.tab-content-leave-active {
  transition: opacity 0.2s ease-in-out;
}

.tab-content-enter-from,
.tab-content-leave-to {
  opacity: 0;
}
</style>
